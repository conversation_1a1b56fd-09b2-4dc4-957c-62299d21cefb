# Firebase Setup Guide

## Required Firestore Indexes

The application requires several composite indexes to function properly. These need to be created in the Firebase Console.

### How to Create Indexes

#### Option 1: Using Firebase CLI (Recommended)
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Initialize Firestore in your project: `firebase init firestore`
4. Use the provided `firestore-indexes.json` file
5. Deploy indexes: `firebase deploy --only firestore:indexes`

#### Option 2: Manual Creation in Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`saluni-mkononi`)
3. Navigate to Firestore Database > Indexes
4. Click "Create Index" for each required index below

### Required Composite Indexes

#### 1. Salons Collection
- **Collection ID**: `salons`
- **Fields**:
  - `isActive` (Ascending)
  - `rating` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 2. Salons by Location
- **Collection ID**: `salons`
- **Fields**:
  - `location` (Ascending)
  - `isActive` (Ascending)
  - `rating` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 3. Services by Salon
- **Collection ID**: `services`
- **Fields**:
  - `salonId` (Ascending)
  - `isActive` (Ascending)
  - `name` (Ascending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 4. Services by Category
- **Collection ID**: `services`
- **Fields**:
  - `category` (Ascending)
  - `isActive` (Ascending)
  - `price` (Ascending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 5. Bookings by Salon and Date
- **Collection ID**: `bookings`
- **Fields**:
  - `salonId` (Ascending)
  - `date` (Descending)
  - `time` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 6. Bookings by Status
- **Collection ID**: `bookings`
- **Fields**:
  - `salonId` (Ascending)
  - `status` (Ascending)
  - `date` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 7. Bookings by Customer
- **Collection ID**: `bookings`
- **Fields**:
  - `customerId` (Ascending)
  - `createdAt` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 8. Staff by Salon
- **Collection ID**: `staff`
- **Fields**:
  - `salonId` (Ascending)
  - `isActive` (Ascending)
  - `name` (Ascending)
  - `__name__` (Ascending) - Auto-added by Firebase

#### 9. Notifications by Salon
- **Collection ID**: `notifications`
- **Fields**:
  - `salonId` (Ascending)
  - `isRead` (Ascending)
  - `createdAt` (Descending)
  - `__name__` (Ascending) - Auto-added by Firebase

### Index Status

After creating indexes, they will show as "Building" in the Firebase Console. Wait for all indexes to show "Enabled" status before using the application.

### Temporary Workarounds

The application currently includes temporary workarounds that:
1. Fetch more data than needed and filter client-side
2. Use single-field queries instead of compound queries
3. Sort results in JavaScript instead of using Firestore ordering

These workarounds will be removed once the proper indexes are created.

## Authentication Setup

### Required Authentication Methods

1. **Email/Password**: Already enabled
2. **Email Verification**: Configured for new accounts

### Test Accounts

Create test accounts in Firebase Authentication Console:

1. **Admin Account**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `admin`

2. **Salon Owner Account**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `salon_owner`

3. **Customer Account**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `customer`

**Steps to create accounts:**
1. Go to Firebase Console > Authentication > Users
2. Click "Add user"
3. Enter email and password
4. After creating the user, you'll need to add user documents in Firestore under the `users` collection with the appropriate role field

## Security Rules

Ensure Firestore Security Rules are properly configured:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Salons are readable by all, writable by owners and admins
    match /salons/{salonId} {
      allow read: if true;
      allow write: if request.auth != null && 
        (resource.data.ownerId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Services are readable by all, writable by salon owners and admins
    match /services/{serviceId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Bookings are readable/writable by customers, salon owners, and admins
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Staff data is readable by all, writable by salon owners and admins
    match /staff/{staffId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## Environment Variables

Ensure your `.env` file has the correct Firebase configuration:

```
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

## Troubleshooting

### Common Errors and Solutions

#### 1. "The query requires an index" Error
**Error**: `FirebaseError: The query requires an index. That index is currently building and cannot be used yet.`

**Solution**:
- Create the required composite indexes (see above)
- Wait for indexes to show "Enabled" status in Firebase Console
- The application includes temporary workarounds that filter data client-side

#### 2. "auth/invalid-credential" Error
**Error**: `Firebase: Error (auth/invalid-credential)`

**Solutions**:
- Verify the email and password are correct
- Ensure the user account exists in Firebase Authentication
- Check that the user document exists in Firestore under the `users` collection
- Verify the `.env` file has correct Firebase configuration

#### 3. Connection Errors
**Error**: `net::ERR_CONNECTION_CLOSED` or similar network errors

**Solutions**:
- Check internet connectivity
- Verify Firebase project ID is correct in `.env`
- Check if Firebase services are enabled for your project
- Try refreshing the page or restarting the development server

#### 4. Missing User Document
**Error**: User can authenticate but gets null user data

**Solution**:
- Ensure user documents exist in Firestore `users` collection
- User documents should have the structure:
  ```json
  {
    "id": "user-uid",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "admin|salon_owner|customer",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
  ```

### Index Building Status
- Indexes can take several minutes to build
- Check status in Firebase Console > Firestore > Indexes
- Don't use the application until all indexes show "Enabled"

### Development Tips
- Use browser developer tools to check console errors
- Monitor Network tab for failed requests
- Check Firebase Console for quota limits and usage
