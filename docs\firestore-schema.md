# Firestore Database Schema

## Collections Overview

### 1. users
- **Purpose**: Store user accounts (admin, salon_owner, customer)
- **Document ID**: Firebase Auth UID
- **Structure**:
```typescript
{
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'salon_owner' | 'customer';
  createdAt: string;
  updatedAt: string;
  
  // For salon owners
  salonId?: string;
  isApproved?: boolean;
  requestedAt?: string;
  approvedAt?: string;
  approvedBy?: string;
  
  // For admins
  permissions?: string[];
}
```

### 2. salons
- **Purpose**: Store salon information
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  name: string;
  description: string;
  location: string;
  address: string;
  distance: string;
  rating: number;
  reviews: number;
  images: string[];
  ownerId: string;
  isActive: boolean;
  coordinates?: {
    lat: number;
    lng: number;
  };
  createdAt: string;
  updatedAt: string;
}
```

### 3. services
- **Purpose**: Store salon services
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  salonId: string;
  name: string;
  description?: string;
  price: number;
  duration: number; // in minutes
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 4. staff
- **Purpose**: Store salon staff information
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  salonId: string;
  name: string;
  email?: string;
  phone?: string;
  specialty: string;
  bio?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 5. staff_schedules
- **Purpose**: Store staff working schedules
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  staffId: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 6. bookings
- **Purpose**: Store customer bookings
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceId: string;
  staffId: string;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  notes?: string;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
}
```

### 7. salon_registration_requests
- **Purpose**: Store salon registration requests
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  ownerName: string;
  ownerEmail: string;
  ownerPhone: string;
  salonName: string;
  salonDescription: string;
  salonAddress: string;
  businessLicense?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
}
```

### 8. notifications
- **Purpose**: Store notifications for salon owners
- **Document ID**: Auto-generated
- **Structure**:
```typescript
{
  id: string;
  salonId: string;
  type: 'booking' | 'review' | 'staff' | 'payment' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  isNew: boolean;
  priority: 'low' | 'medium' | 'high';
  relatedId?: string; // ID of related booking, review, etc.
  createdAt: string;
  readAt?: string;
}
```

## Security Rules

### Basic Security Rules Structure:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Salons - public read, owner/admin write
    match /salons/{salonId} {
      allow read: if true; // Public read for browsing
      allow write: if request.auth != null && (
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == salonId)
      );
    }
    
    // Services - public read, salon owner/admin write
    match /services/{serviceId} {
      allow read: if true;
      allow write: if request.auth != null && (
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == resource.data.salonId)
      );
    }
    
    // Staff - salon owner/admin access
    match /staff/{staffId} {
      allow read, write: if request.auth != null && (
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == resource.data.salonId)
      );
    }
    
    // Bookings - customers can create, salon owners and admins can manage
    match /bookings/{bookingId} {
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.customerId ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == resource.data.salonId)
      );
      allow create: if request.auth != null;
      allow update: if request.auth != null && (
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == resource.data.salonId)
      );
    }
    
    // Salon registration requests - admin only
    match /salon_registration_requests/{requestId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      allow create: if true; // Anyone can submit a request
    }
    
    // Notifications - salon owner access
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && (
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'salon_owner' &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.salonId == resource.data.salonId)
      );
    }
  }
}
```

## Indexes

### Composite Indexes Needed:
1. **services**: `salonId` (Ascending), `isActive` (Ascending)
2. **staff**: `salonId` (Ascending), `isActive` (Ascending)
3. **bookings**: `salonId` (Ascending), `date` (Ascending)
4. **bookings**: `customerId` (Ascending), `createdAt` (Descending)
5. **notifications**: `salonId` (Ascending), `isRead` (Ascending), `createdAt` (Descending)
6. **salons**: `isActive` (Ascending), `rating` (Descending)
7. **salons**: `location` (Ascending), `rating` (Descending)

## Data Migration Strategy

1. **Phase 1**: Set up collections and security rules
2. **Phase 2**: Migrate user authentication to Firebase Auth
3. **Phase 3**: Migrate salon data with proper relationships
4. **Phase 4**: Migrate services and staff data
5. **Phase 5**: Migrate booking data
6. **Phase 6**: Set up real-time listeners and notifications
