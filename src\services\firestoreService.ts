import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  QueryConstraint,
  onSnapshot,
  Timestamp,
  writeBatch,
  FirestoreError
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
};

// Helper function to check if error is retryable
const isRetryableError = (error: any): boolean => {
  if (error?.code) {
    // Retryable Firestore error codes
    const retryableCodes = [
      'unavailable',
      'deadline-exceeded',
      'resource-exhausted',
      'aborted',
      'internal',
      'cancelled'
    ];
    return retryableCodes.includes(error.code);
  }
  return false;
};

// Helper function to implement exponential backoff
const delay = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

// Retry wrapper for async operations
const withRetry = async <T>(
  operation: () => Promise<T>,
  context: string,
  retries = RETRY_CONFIG.maxRetries
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    console.error(`${context} attempt failed:`, error);

    if (retries > 0 && isRetryableError(error)) {
      const delayMs = Math.min(
        RETRY_CONFIG.baseDelay * Math.pow(2, RETRY_CONFIG.maxRetries - retries),
        RETRY_CONFIG.maxDelay
      );

      console.log(`Retrying ${context} in ${delayMs}ms... (${retries} retries left)`);
      await delay(delayMs);
      return withRetry(operation, context, retries - 1);
    }

    throw error;
  }
};

export class FirestoreService {
  // Generic CRUD operations
  
  // Create document
  static async create<T>(collectionName: string, data: Omit<T, 'id'>): Promise<string> {
    return withRetry(async () => {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      return docRef.id;
    }, `Creating document in ${collectionName}`);
  }

  // Create document with custom ID
  static async createWithId<T>(
    collectionName: string, 
    id: string, 
    data: Omit<T, 'id'>
  ): Promise<void> {
    try {
      await updateDoc(doc(db, collectionName, id), {
        ...data,
        id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Error creating document with ID in ${collectionName}:`, error);
      throw error;
    }
  }

  // Get document by ID
  static async getById<T>(collectionName: string, id: string): Promise<T | null> {
    return withRetry(async () => {
      const docSnap = await getDoc(doc(db, collectionName, id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as T;
      }
      return null;
    }, `Getting document ${id} from ${collectionName}`);
  }

  // Get all documents
  static async getAll<T>(collectionName: string): Promise<T[]> {
    return withRetry(async () => {
      const querySnapshot = await getDocs(collection(db, collectionName));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
    }, `Getting all documents from ${collectionName}`);
  }

  // Get documents with query
  static async getWithQuery<T>(
    collectionName: string,
    constraints: QueryConstraint[]
  ): Promise<T[]> {
    return withRetry(async () => {
      const q = query(collection(db, collectionName), ...constraints);
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
    }, `Querying documents from ${collectionName}`);
  }

  // Update document
  static async update<T>(
    collectionName: string,
    id: string,
    updates: Partial<T>
  ): Promise<void> {
    return withRetry(async () => {
      await updateDoc(doc(db, collectionName, id), {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    }, `Updating document ${id} in ${collectionName}`);
  }

  // Delete document
  static async delete(collectionName: string, id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, collectionName, id));
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      throw error;
    }
  }

  // Listen to document changes
  static onDocumentChange<T>(
    collectionName: string,
    id: string,
    callback: (data: T | null) => void
  ): () => void {
    return onSnapshot(doc(db, collectionName, id), (doc) => {
      if (doc.exists()) {
        callback({ id: doc.id, ...doc.data() } as T);
      } else {
        callback(null);
      }
    });
  }

  // Listen to collection changes
  static onCollectionChange<T>(
    collectionName: string,
    callback: (data: T[]) => void,
    constraints: QueryConstraint[] = []
  ): () => void {
    const q = constraints.length > 0 
      ? query(collection(db, collectionName), ...constraints)
      : collection(db, collectionName);
      
    return onSnapshot(q, (querySnapshot) => {
      const data = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
      callback(data);
    });
  }

  // Batch operations
  static async batchWrite(operations: Array<{
    type: 'create' | 'update' | 'delete';
    collection: string;
    id?: string;
    data?: any;
  }>): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      operations.forEach(op => {
        const docRef = op.id 
          ? doc(db, op.collection, op.id)
          : doc(collection(db, op.collection));
          
        switch (op.type) {
          case 'create':
            batch.set(docRef, {
              ...op.data,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            });
            break;
          case 'update':
            batch.update(docRef, {
              ...op.data,
              updatedAt: new Date().toISOString()
            });
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error in batch write:', error);
      throw error;
    }
  }

  // Paginated query
  static async getPaginated<T>(
    collectionName: string,
    pageSize: number,
    lastDoc?: DocumentSnapshot,
    constraints: QueryConstraint[] = []
  ): Promise<{ data: T[]; lastDoc: DocumentSnapshot | null }> {
    try {
      const queryConstraints = [
        ...constraints,
        limit(pageSize)
      ];
      
      if (lastDoc) {
        queryConstraints.push(startAfter(lastDoc));
      }
      
      const q = query(collection(db, collectionName), ...queryConstraints);
      const querySnapshot = await getDocs(q);
      
      const data = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[];
      
      const lastDocument = querySnapshot.docs[querySnapshot.docs.length - 1] || null;
      
      return { data, lastDoc: lastDocument };
    } catch (error) {
      console.error(`Error in paginated query for ${collectionName}:`, error);
      throw error;
    }
  }
}
