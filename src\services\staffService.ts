import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Staff, StaffForm, StaffSchedule } from '@/types';

export class StaffService {
  private static readonly COLLECTION = 'staff';
  private static readonly SCHEDULES_COLLECTION = 'staff_schedules';

  // Create a new staff member
  static async createStaff(staffData: StaffForm & { salonId: string }): Promise<string> {
    try {
      const staff: Omit<Staff, 'id'> = {
        ...staffData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<Staff>(this.COLLECTION, staff);
    } catch (error) {
      console.error('Error creating staff:', error);
      throw error;
    }
  }

  // Get staff by ID
  static async getStaffById(id: string): Promise<Staff | null> {
    try {
      return await FirestoreService.getById<Staff>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting staff:', error);
      throw error;
    }
  }

  // Get staff by salon
  static async getStaffBySalon(salonId: string): Promise<Staff[]> {
    try {
      return await FirestoreService.getWithQuery<Staff>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('isActive', '==', true),
        orderBy('name', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting staff by salon:', error);
      throw error;
    }
  }

  // Get all active staff
  static async getActiveStaff(): Promise<Staff[]> {
    try {
      return await FirestoreService.getWithQuery<Staff>(this.COLLECTION, [
        where('isActive', '==', true),
        orderBy('name', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting active staff:', error);
      throw error;
    }
  }

  // Update staff
  static async updateStaff(id: string, updates: Partial<Staff>): Promise<void> {
    try {
      await FirestoreService.update<Staff>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating staff:', error);
      throw error;
    }
  }

  // Delete staff (soft delete)
  static async deleteStaff(id: string): Promise<void> {
    try {
      await FirestoreService.update<Staff>(this.COLLECTION, id, { 
        isActive: false 
      });
    } catch (error) {
      console.error('Error deleting staff:', error);
      throw error;
    }
  }

  // Search staff by name or specialty
  static async searchStaff(searchTerm: string, salonId?: string): Promise<Staff[]> {
    try {
      let staff: Staff[];
      
      if (salonId) {
        staff = await this.getStaffBySalon(salonId);
      } else {
        staff = await this.getActiveStaff();
      }
      
      return staff.filter(member => 
        member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (member.bio && member.bio.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    } catch (error) {
      console.error('Error searching staff:', error);
      throw error;
    }
  }

  // Get all staff (admin only)
  static async getAllStaff(): Promise<Staff[]> {
    try {
      return await FirestoreService.getAll<Staff>(this.COLLECTION);
    } catch (error) {
      console.error('Error getting all staff:', error);
      throw error;
    }
  }

  // Staff Schedule Management

  // Create staff schedule
  static async createStaffSchedule(scheduleData: Omit<StaffSchedule, 'id'>): Promise<string> {
    try {
      return await FirestoreService.create<StaffSchedule>(this.SCHEDULES_COLLECTION, scheduleData);
    } catch (error) {
      console.error('Error creating staff schedule:', error);
      throw error;
    }
  }

  // Get staff schedule
  static async getStaffSchedule(staffId: string): Promise<StaffSchedule[]> {
    try {
      return await FirestoreService.getWithQuery<StaffSchedule>(this.SCHEDULES_COLLECTION, [
        where('staffId', '==', staffId),
        orderBy('dayOfWeek', 'asc')
      ]);
    } catch (error) {
      console.error('Error getting staff schedule:', error);
      throw error;
    }
  }

  // Update staff schedule
  static async updateStaffSchedule(id: string, updates: Partial<StaffSchedule>): Promise<void> {
    try {
      await FirestoreService.update<StaffSchedule>(this.SCHEDULES_COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating staff schedule:', error);
      throw error;
    }
  }

  // Delete staff schedule
  static async deleteStaffSchedule(id: string): Promise<void> {
    try {
      await FirestoreService.delete(this.SCHEDULES_COLLECTION, id);
    } catch (error) {
      console.error('Error deleting staff schedule:', error);
      throw error;
    }
  }

  // Get available staff for a specific day and time
  static async getAvailableStaff(
    salonId: string, 
    dayOfWeek: number, 
    time: string
  ): Promise<Staff[]> {
    try {
      const salonStaff = await this.getStaffBySalon(salonId);
      const availableStaff: Staff[] = [];

      for (const staff of salonStaff) {
        const schedules = await this.getStaffSchedule(staff.id);
        const daySchedule = schedules.find(s => s.dayOfWeek === dayOfWeek && s.isAvailable);
        
        if (daySchedule && this.isTimeInRange(time, daySchedule.startTime, daySchedule.endTime)) {
          availableStaff.push(staff);
        }
      }

      return availableStaff;
    } catch (error) {
      console.error('Error getting available staff:', error);
      throw error;
    }
  }

  // Helper method to check if time is within range
  private static isTimeInRange(time: string, startTime: string, endTime: string): boolean {
    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    
    return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
  }

  // Helper method to convert time string to minutes
  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Listen to staff changes for a salon
  static onSalonStaffChange(salonId: string, callback: (staff: Staff[]) => void): () => void {
    return FirestoreService.onCollectionChange<Staff>(this.COLLECTION, callback, [
      where('salonId', '==', salonId),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    ]);
  }

  // Batch update staff
  static async batchUpdateStaff(updates: Array<{ id: string; data: Partial<Staff> }>): Promise<void> {
    try {
      const operations = updates.map(update => ({
        type: 'update' as const,
        collection: this.COLLECTION,
        id: update.id,
        data: update.data
      }));
      
      await FirestoreService.batchWrite(operations);
    } catch (error) {
      console.error('Error batch updating staff:', error);
      throw error;
    }
  }
}
