import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock,
  Star,
  Bell,
  Plus,
  Eye
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { AnalyticsService } from '@/services/analyticsService';
import { useAuth } from '@/contexts/AuthContext';
import { Booking } from '@/types';
import { toast } from '@/components/ui/sonner';

const SalonOwnerDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalBookings: 0,
    monthlyRevenue: 0,
    totalStaff: 0,
    avgRating: 0,
    todayBookings: 0,
    pendingBookings: 0,
  });
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  // Load dashboard data from Firebase
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user || user.role !== 'salon_owner' || !user.salonId) return;

      try {
        setLoading(true);

        // Load stats and recent bookings
        const [statsData, bookingsData] = await Promise.all([
          AnalyticsService.getSalonOwnerDashboardStats(user.salonId),
          AnalyticsService.getRecentBookings(user.salonId, 5)
        ]);

        setStats(statsData);
        setRecentBookings(bookingsData);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  const upcomingAppointments = [
    {
      id: '1',
      customerName: 'Lisa Wilson',
      service: 'Manicure',
      staff: 'Emily Carter',
      time: '9:00 AM',
      date: 'Tomorrow',
    },
    {
      id: '2',
      customerName: 'John Smith',
      service: 'Haircut',
      staff: 'Sophia Bennett',
      time: '11:30 AM',
      date: 'Tomorrow',
    },
    {
      id: '3',
      customerName: 'Anna Brown',
      service: 'Hair Treatment',
      staff: 'Emily Carter',
      time: '2:00 PM',
      date: 'Tomorrow',
    },
  ];

  const notifications = [
    {
      id: '1',
      type: 'booking',
      message: 'New booking request from Sarah Johnson',
      time: '5 minutes ago',
      isNew: true,
    },
    {
      id: '2',
      type: 'review',
      message: 'New 5-star review from Mike Chen',
      time: '1 hour ago',
      isNew: true,
    },
    {
      id: '3',
      type: 'staff',
      message: 'Emily Carter updated her schedule',
      time: '2 hours ago',
      isNew: false,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Dashboard</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Welcome back! Here's what's happening with your salon today.
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" asChild>
            <Link to="/salon-owner/bookings">
              <Eye className="w-4 h-4 mr-2" />
              View All Bookings
            </Link>
          </Button>
          <Button className="bg-glamspot-primary hover:bg-glamspot-primary-dark" asChild>
            <Link to="/salon-owner/services">
              <Plus className="w-4 h-4 mr-2" />
              Add Service
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Today's Bookings
            </CardTitle>
            <Calendar className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.todayBookings}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">
              <Clock className="h-3 w-3 mr-1" />
              {stats.pendingBookings} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Monthly Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              ${stats.monthlyRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Staff
            </CardTitle>
            <Users className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.totalStaff}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              All active staff members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Rating
            </CardTitle>
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.avgRating}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +0.2 from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Bookings */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-glamspot-primary" />
              Recent Bookings
            </CardTitle>
            <CardDescription>
              Latest booking activity for your salon
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 bg-glamspot-neutral-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-glamspot-neutral-900">{booking.customerName}</p>
                    <p className="text-sm text-glamspot-neutral-600">{booking.service}</p>
                    <p className="text-sm text-glamspot-neutral-500">{booking.time}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-glamspot-neutral-900">${booking.amount}</p>
                    <Badge 
                      variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                      className="mt-1"
                    >
                      {booking.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-glamspot-primary" />
              Notifications
            </CardTitle>
            <CardDescription>
              Recent updates and alerts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div key={notification.id} className="flex items-start gap-3 p-3 bg-glamspot-neutral-50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full mt-2 ${notification.isNew ? 'bg-glamspot-primary' : 'bg-glamspot-neutral-300'}`} />
                  <div className="flex-1">
                    <p className="text-sm text-glamspot-neutral-900">{notification.message}</p>
                    <p className="text-xs text-glamspot-neutral-500 mt-1">{notification.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Appointments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-glamspot-primary" />
            Upcoming Appointments
          </CardTitle>
          <CardDescription>
            Tomorrow's scheduled appointments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {upcomingAppointments.map((appointment) => (
              <div key={appointment.id} className="p-4 border border-glamspot-neutral-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium text-glamspot-neutral-900">{appointment.customerName}</p>
                  <Badge variant="outline">{appointment.date}</Badge>
                </div>
                <p className="text-sm text-glamspot-neutral-600">{appointment.service}</p>
                <p className="text-sm text-glamspot-neutral-500">with {appointment.staff}</p>
                <p className="text-sm font-medium text-glamspot-primary mt-2">{appointment.time}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SalonOwnerDashboard;
