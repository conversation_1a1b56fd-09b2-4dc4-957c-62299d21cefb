import { AuthService } from './src/services/authService';
import { SalonService } from './src/services/salonService';
import { ServiceService } from './src/services/serviceService';
import { StaffService } from './src/services/staffService';
import { User, Salon, Service, Staff } from './src/types';

/**
 * <PERSON><PERSON><PERSON> to set up test data for the salon application
 * Run this script to create test users, salons, services, and staff
 */

const setupTestData = async () => {
  console.log('Setting up test data...');

  try {
    // Create test users
    console.log('Creating test users...');
    
    // Admin user
    const adminUser: Partial<User> = {
      name: 'Admin User',
      role: 'admin',
      phone: '+**********'
    };

    // Salon owner user
    const ownerUser: Partial<User> = {
      name: 'Salon Owner',
      role: 'salon_owner',
      phone: '+**********'
    };

    // Customer user
    const customerUser: Partial<User> = {
      name: 'Customer User',
      role: 'customer',
      phone: '+**********'
    };

    // Note: You'll need to create these accounts manually in Firebase Authentication
    // or use Firebase Admin SDK for programmatic creation
    console.log('✓ Test user data prepared');
    console.log('Please create these accounts in Firebase Authentication:');
    console.log('1. <EMAIL> (password: password123) - Admin');
    console.log('2. <EMAIL> (password: password123) - Salon Owner');
    console.log('3. <EMAIL> (password: password123) - Customer');

    // Create test salons
    console.log('Creating test salons...');
    
    const testSalons = [
      {
        name: 'Glamour Studio',
        description: 'Premium beauty salon offering hair, nails, and spa services',
        location: 'San Francisco, CA',
        address: '123 Beauty Street, San Francisco, CA 94102',
        phone: '+****************',
        email: '<EMAIL>',
        website: 'https://glamourstudio.com',
        images: [
          'https://images.unsplash.com/photo-**********-138dadb4c035?w=800',
          'https://images.unsplash.com/photo-*************-8b13dee7a37e?w=800'
        ],
        amenities: ['WiFi', 'Parking', 'Air Conditioning', 'Refreshments'],
        socialMedia: {
          instagram: '@glamourstudio',
          facebook: 'GlamourStudioSF'
        },
        businessHours: {
          monday: { open: '09:00', close: '18:00', isOpen: true },
          tuesday: { open: '09:00', close: '18:00', isOpen: true },
          wednesday: { open: '09:00', close: '18:00', isOpen: true },
          thursday: { open: '09:00', close: '20:00', isOpen: true },
          friday: { open: '09:00', close: '20:00', isOpen: true },
          saturday: { open: '08:00', close: '17:00', isOpen: true },
          sunday: { open: '10:00', close: '16:00', isOpen: true }
        },
        ownerId: 'owner-user-id' // Replace with actual owner user ID
      },
      {
        name: 'Bella Beauty Bar',
        description: 'Modern salon specializing in hair styling and color',
        location: 'San Francisco, CA',
        address: '456 Style Avenue, San Francisco, CA 94103',
        phone: '+****************',
        email: '<EMAIL>',
        website: 'https://bellabeautybar.com',
        images: [
          'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=800',
          'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=800'
        ],
        amenities: ['WiFi', 'Parking', 'Coffee Bar'],
        socialMedia: {
          instagram: '@bellabeautybar',
          facebook: 'BellaBeautyBarSF'
        },
        businessHours: {
          monday: { open: '10:00', close: '19:00', isOpen: true },
          tuesday: { open: '10:00', close: '19:00', isOpen: true },
          wednesday: { open: '10:00', close: '19:00', isOpen: true },
          thursday: { open: '10:00', close: '21:00', isOpen: true },
          friday: { open: '10:00', close: '21:00', isOpen: true },
          saturday: { open: '09:00', close: '18:00', isOpen: true },
          sunday: { open: '11:00', close: '17:00', isOpen: true }
        },
        ownerId: 'owner-user-id' // Replace with actual owner user ID
      }
    ];

    // Create test services
    console.log('Creating test services...');
    
    const testServices = [
      {
        name: 'Haircut & Style',
        description: 'Professional haircut with styling',
        category: 'Hair',
        price: 65,
        duration: 60,
        salonId: 'salon-id-1', // Replace with actual salon ID
        isActive: true
      },
      {
        name: 'Hair Color',
        description: 'Full hair coloring service',
        category: 'Hair',
        price: 120,
        duration: 120,
        salonId: 'salon-id-1',
        isActive: true
      },
      {
        name: 'Manicure',
        description: 'Classic manicure with polish',
        category: 'Nails',
        price: 35,
        duration: 45,
        salonId: 'salon-id-1',
        isActive: true
      },
      {
        name: 'Facial Treatment',
        description: 'Relaxing facial with skincare',
        category: 'Skincare',
        price: 85,
        duration: 75,
        salonId: 'salon-id-1',
        isActive: true
      }
    ];

    // Create test staff
    console.log('Creating test staff...');
    
    const testStaff = [
      {
        name: 'Sarah Johnson',
        role: 'Senior Stylist',
        specialties: ['Hair Cutting', 'Hair Coloring', 'Styling'],
        bio: 'Sarah has over 10 years of experience in hair styling and specializes in modern cuts and color techniques.',
        image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
        salonId: 'salon-id-1',
        isActive: true
      },
      {
        name: 'Maria Rodriguez',
        role: 'Nail Technician',
        specialties: ['Manicures', 'Pedicures', 'Nail Art'],
        bio: 'Maria is a certified nail technician with expertise in nail art and gel applications.',
        image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
        salonId: 'salon-id-1',
        isActive: true
      },
      {
        name: 'Emily Chen',
        role: 'Esthetician',
        specialties: ['Facials', 'Skincare', 'Eyebrow Shaping'],
        bio: 'Emily specializes in skincare treatments and has a passion for helping clients achieve healthy, glowing skin.',
        image: 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=400',
        salonId: 'salon-id-1',
        isActive: true
      }
    ];

    console.log('✓ Test data structure prepared');
    console.log('');
    console.log('To complete the setup:');
    console.log('1. Create user accounts in Firebase Authentication');
    console.log('2. Note down the user IDs');
    console.log('3. Create salons using the salon registration form or admin panel');
    console.log('4. Add services and staff through the admin interface');
    console.log('');
    console.log('Sample data is ready to be imported!');

  } catch (error) {
    console.error('Error setting up test data:', error);
  }
};

// Export for use in other scripts
export { setupTestData };

// Run if called directly
if (require.main === module) {
  setupTestData();
}
