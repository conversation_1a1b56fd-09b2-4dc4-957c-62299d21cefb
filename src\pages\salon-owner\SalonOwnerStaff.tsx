import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Edit, Calendar, Clock, Users, UserCheck } from 'lucide-react';
import { toast } from 'sonner';
import { Staff, StaffSchedule } from '@/types';
import { StaffService } from '@/services/staffService';
import { useAuth } from '@/contexts/AuthContext';

const SalonOwnerStaff = () => {
  const { user } = useAuth();
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);

  // Load staff from Firebase
  useEffect(() => {
    const loadStaff = async () => {
      if (!user || user.role !== 'salon_owner' || !user.salonId) return;

      try {
        setLoading(true);
        const staffData = await StaffService.getStaffBySalon(user.salonId);
        setStaff(staffData);
      } catch (error) {
        console.error('Error loading staff:', error);
        toast.error('Failed to load staff');
      } finally {
        setLoading(false);
      }
    };

    loadStaff();

    // Set up real-time listener
    if (user?.salonId) {
      const unsubscribe = StaffService.onSalonStaffChange(user.salonId, (staffData) => {
        setStaff(staffData);
      });

      return () => unsubscribe();
    }
  }, [user]);

  // Mock schedule data
  const [schedules, setSchedules] = useState<StaffSchedule[]>([
    {
      id: 'schedule-1',
      staffId: 'staff-1',
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
    },
    {
      id: 'schedule-2',
      staffId: 'staff-1',
      dayOfWeek: 2, // Tuesday
      startTime: '09:00',
      endTime: '17:00',
      isAvailable: true,
    },
    {
      id: 'schedule-3',
      staffId: 'staff-1',
      dayOfWeek: 3, // Wednesday
      startTime: '10:00',
      endTime: '18:00',
      isAvailable: true,
    },
    {
      id: 'schedule-4',
      staffId: 'staff-2',
      dayOfWeek: 1, // Monday
      startTime: '10:00',
      endTime: '16:00',
      isAvailable: true,
    },
  ]);

  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false);
  const [isEditStaffDialogOpen, setIsEditStaffDialogOpen] = useState(false);
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null);
  const [selectedStaffForSchedule, setSelectedStaffForSchedule] = useState<Staff | null>(null);
  
  const [staffFormData, setStaffFormData] = useState({
    name: '',
    email: '',
    phone: '',
    specialty: '',
    bio: '',
  });

  const daysOfWeek = [
    { value: 0, label: 'Sunday' },
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' },
  ];

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return `${hour}:00`;
  });

  const handleAddStaff = () => {
    setStaffFormData({
      name: '',
      email: '',
      phone: '',
      specialty: '',
      bio: '',
    });
    setIsAddStaffDialogOpen(true);
  };

  const handleEditStaff = (staffMember: Staff) => {
    setEditingStaff(staffMember);
    setStaffFormData({
      name: staffMember.name,
      email: staffMember.email || '',
      phone: staffMember.phone || '',
      specialty: staffMember.specialty,
      bio: staffMember.bio || '',
    });
    setIsEditStaffDialogOpen(true);
  };

  const handleSubmitStaffForm = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!staffFormData.name || !staffFormData.specialty) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (editingStaff) {
      // Update existing staff
      setStaff(prev =>
        prev.map(member =>
          member.id === editingStaff.id
            ? { ...member, ...staffFormData, updatedAt: new Date().toISOString() }
            : member
        )
      );
      toast.success(`Staff member "${staffFormData.name}" has been updated`);
      setIsEditStaffDialogOpen(false);
      setEditingStaff(null);
    } else {
      // Add new staff
      const newStaff: Staff = {
        id: `staff-${Date.now()}`,
        salonId: '1', // In real app, get from auth context
        ...staffFormData,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setStaff(prev => [...prev, newStaff]);
      toast.success(`Staff member "${staffFormData.name}" has been added`);
      setIsAddStaffDialogOpen(false);
    }
    
    setStaffFormData({
      name: '',
      email: '',
      phone: '',
      specialty: '',
      bio: '',
    });
  };

  const handleToggleStaffActive = (staffId: string) => {
    setStaff(prev =>
      prev.map(member =>
        member.id === staffId
          ? { ...member, isActive: !member.isActive, updatedAt: new Date().toISOString() }
          : member
      )
    );
    toast.success('Staff status updated');
  };

  const handleManageSchedule = (staffMember: Staff) => {
    setSelectedStaffForSchedule(staffMember);
    setIsScheduleDialogOpen(true);
  };

  const getStaffSchedule = (staffId: string) => {
    return schedules.filter(schedule => schedule.staffId === staffId);
  };

  const handleScheduleChange = (staffId: string, dayOfWeek: number, field: string, value: string | boolean) => {
    setSchedules(prev => {
      const existingSchedule = prev.find(s => s.staffId === staffId && s.dayOfWeek === dayOfWeek);
      
      if (existingSchedule) {
        return prev.map(schedule =>
          schedule.id === existingSchedule.id
            ? { ...schedule, [field]: value }
            : schedule
        );
      } else {
        // Create new schedule entry
        const newSchedule: StaffSchedule = {
          id: `schedule-${Date.now()}-${dayOfWeek}`,
          staffId,
          dayOfWeek,
          startTime: field === 'startTime' ? value as string : '09:00',
          endTime: field === 'endTime' ? value as string : '17:00',
          isAvailable: field === 'isAvailable' ? value as boolean : true,
        };
        return [...prev, newSchedule];
      }
    });
  };

  const StaffForm = () => (
    <form onSubmit={handleSubmitStaffForm} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            value={staffFormData.name}
            onChange={(e) => setStaffFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Emily Carter"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={staffFormData.email}
            onChange={(e) => setStaffFormData(prev => ({ ...prev, email: e.target.value }))}
            placeholder="<EMAIL>"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={staffFormData.phone}
            onChange={(e) => setStaffFormData(prev => ({ ...prev, phone: e.target.value }))}
            placeholder="(*************"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="specialty">Specialty *</Label>
          <Input
            id="specialty"
            value={staffFormData.specialty}
            onChange={(e) => setStaffFormData(prev => ({ ...prev, specialty: e.target.value }))}
            placeholder="e.g., Hair Styling & Coloring"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={staffFormData.bio}
          onChange={(e) => setStaffFormData(prev => ({ ...prev, bio: e.target.value }))}
          placeholder="Brief description of experience and skills..."
          rows={3}
        />
      </div>
      
      <DialogFooter>
        <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          {editingStaff ? 'Update Staff' : 'Add Staff'}
        </Button>
      </DialogFooter>
    </form>
  );

  const ScheduleManager = () => {
    if (!selectedStaffForSchedule) return null;

    const staffSchedule = getStaffSchedule(selectedStaffForSchedule.id);

    return (
      <div className="space-y-4">
        <div className="text-sm text-glamspot-neutral-600 mb-4">
          Set weekly schedule for {selectedStaffForSchedule.name}
        </div>
        
        <div className="space-y-3">
          {daysOfWeek.map((day) => {
            const daySchedule = staffSchedule.find(s => s.dayOfWeek === day.value);
            
            return (
              <div key={day.value} className="flex items-center gap-4 p-3 border rounded-lg">
                <div className="w-20 text-sm font-medium">
                  {day.label}
                </div>
                
                <div className="flex items-center gap-2">
                  <Switch
                    checked={daySchedule?.isAvailable || false}
                    onCheckedChange={(checked) => 
                      handleScheduleChange(selectedStaffForSchedule.id, day.value, 'isAvailable', checked)
                    }
                  />
                  <span className="text-sm">Available</span>
                </div>
                
                {daySchedule?.isAvailable && (
                  <>
                    <div className="flex items-center gap-2">
                      <Label className="text-sm">From:</Label>
                      <Select
                        value={daySchedule.startTime}
                        onValueChange={(value) => 
                          handleScheduleChange(selectedStaffForSchedule.id, day.value, 'startTime', value)
                        }
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Label className="text-sm">To:</Label>
                      <Select
                        value={daySchedule.endTime}
                        onValueChange={(value) => 
                          handleScheduleChange(selectedStaffForSchedule.id, day.value, 'endTime', value)
                        }
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
        
        <DialogFooter>
          <Button 
            onClick={() => {
              toast.success(`Schedule updated for ${selectedStaffForSchedule.name}`);
              setIsScheduleDialogOpen(false);
            }}
            className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
          >
            Save Schedule
          </Button>
        </DialogFooter>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Staff Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage your team and their schedules
          </p>
        </div>
        <Dialog open={isAddStaffDialogOpen} onOpenChange={setIsAddStaffDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddStaff} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
              <Plus className="w-4 h-4 mr-2" />
              Add Staff
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Staff Member</DialogTitle>
              <DialogDescription>
                Add a new team member to your salon
              </DialogDescription>
            </DialogHeader>
            <StaffForm />
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Staff
            </CardTitle>
            <Users className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{staff.length}</div>
            <p className="text-xs text-green-600 mt-1">
              {staff.filter(s => s.isActive).length} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Scheduled Today
            </CardTitle>
            <UserCheck className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              {staff.filter(s => s.isActive).length}
            </div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Available staff members
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Schedule Coverage
            </CardTitle>
            <Calendar className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">85%</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Weekly coverage
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Staff Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-glamspot-primary" />
            Your Team
          </CardTitle>
          <CardDescription>
            Manage your staff members and their information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Staff Member</TableHead>
                <TableHead>Specialty</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {staff.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium text-glamspot-neutral-900">{member.name}</p>
                      {member.bio && (
                        <p className="text-sm text-glamspot-neutral-500 mt-1">{member.bio}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{member.specialty}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {member.email && <div>{member.email}</div>}
                      {member.phone && <div className="text-glamspot-neutral-500">{member.phone}</div>}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={member.isActive}
                        onCheckedChange={() => handleToggleStaffActive(member.id)}
                      />
                      <span className={`text-sm ${member.isActive ? 'text-green-600' : 'text-red-600'}`}>
                        {member.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleManageSchedule(member)}
                      >
                        <Calendar className="w-4 h-4 mr-1" />
                        Schedule
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditStaff(member)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Staff Dialog */}
      <Dialog open={isEditStaffDialogOpen} onOpenChange={setIsEditStaffDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Staff Member</DialogTitle>
            <DialogDescription>
              Update staff member information
            </DialogDescription>
          </DialogHeader>
          <StaffForm />
        </DialogContent>
      </Dialog>

      {/* Schedule Management Dialog */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Manage Schedule</DialogTitle>
            <DialogDescription>
              Set weekly availability and working hours
            </DialogDescription>
          </DialogHeader>
          <ScheduleManager />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonOwnerStaff;
