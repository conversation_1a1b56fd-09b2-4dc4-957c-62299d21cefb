import { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { SearchBar } from "./SearchBar";
import { Heart, Globe, Menu, User, LogOut, Settings, LayoutDashboard } from "lucide-react";

export const Header = () => {
  const { user, isAuthenticated, isAdmin, isSalonOwner, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-glamspot-neutral-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 gap-8">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 text-glamspot-primary hover:text-glamspot-primary-dark transition-colors">
            <div className="w-8 h-8 bg-glamspot-primary rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-white fill-current" />
            </div>
            <h1 className="text-2xl font-bold tracking-tight hidden sm:block">GlamSpot</h1>
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl">
            <SearchBar />
          </div>

          {/* Right Navigation */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" className="hidden lg:flex text-glamspot-neutral-700 hover:text-glamspot-primary" asChild>
              <Link to="/register-salon">List your space</Link>
            </Button>

            <Button variant="ghost" size="icon" className="text-glamspot-neutral-700 hover:text-glamspot-primary">
              <Globe className="w-5 h-5" />
            </Button>

            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 border border-glamspot-neutral-200 rounded-full p-2 hover:shadow-md transition-shadow">
                    <Menu className="w-4 h-4 text-glamspot-neutral-700" />
                    <div className="w-8 h-8 bg-glamspot-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {user?.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user?.name}</p>
                      <p className="text-xs text-glamspot-neutral-500">{user?.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {isAdmin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center">
                        <LayoutDashboard className="w-4 h-4 mr-2" />
                        Admin Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  {isSalonOwner && (
                    <DropdownMenuItem asChild>
                      <Link to="/salon-owner" className="flex items-center">
                        <Settings className="w-4 h-4 mr-2" />
                        Salon Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="ghost" asChild>
                  <Link to="/login">Sign in</Link>
                </Button>
                <div className="flex items-center gap-2 border border-glamspot-neutral-200 rounded-full p-2 hover:shadow-md transition-shadow">
                  <Menu className="w-4 h-4 text-glamspot-neutral-700" />
                  <div className="w-8 h-8 bg-glamspot-neutral-500 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};