import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Search, Calendar as CalendarIcon, MapPin, Users } from "lucide-react";
import { format } from "date-fns";

export const SearchBar = () => {
  const { filters, setFilters, searchQuery, setSearchQuery } = useSearchFilter();
  const [isLocationOpen, setIsLocationOpen] = useState(false);
  const [isCheckInOpen, setIsCheckInOpen] = useState(false);
  const [isCheckOutOpen, setIsCheckOutOpen] = useState(false);
  const [isGuestsOpen, setIsGuestsOpen] = useState(false);
  const [checkInDate, setCheckInDate] = useState<Date>();
  const [checkOutDate, setCheckOutDate] = useState<Date>();

  const handleLocationChange = (location: string) => {
    setFilters({ location });
    setIsLocationOpen(false);
  };

  const handleCheckInSelect = (date: Date | undefined) => {
    if (date) {
      setCheckInDate(date);
      setFilters({ checkIn: format(date, 'yyyy-MM-dd') });
      setIsCheckInOpen(false);
    }
  };

  const handleCheckOutSelect = (date: Date | undefined) => {
    if (date) {
      setCheckOutDate(date);
      setFilters({ checkOut: format(date, 'yyyy-MM-dd') });
      setIsCheckOutOpen(false);
    }
  };

  const handleGuestsChange = (guests: number) => {
    setFilters({ guests });
    setIsGuestsOpen(false);
  };

  const handleSearch = () => {
    // The search is automatically triggered by the context when filters change
    // This could trigger additional actions like analytics tracking
    console.log('Search triggered with:', { searchQuery, filters });
  };

  return (
    <div className="flex items-center border border-glamspot-neutral-200 rounded-full shadow-sm hover:shadow-md transition-shadow bg-white">
      {/* Location */}
      <Popover open={isLocationOpen} onOpenChange={setIsLocationOpen}>
        <PopoverTrigger asChild>
          <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 rounded-l-full transition-colors">
            <div className="text-sm font-medium">
              {filters.location || "Anywhere"}
            </div>
            <div className="text-xs text-glamspot-neutral-500">Where to?</div>
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="start">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-glamspot-neutral-500" />
              <Input
                placeholder="Search destinations"
                value={filters.location}
                onChange={(e) => setFilters({ location: e.target.value })}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    setIsLocationOpen(false);
                  }
                }}
              />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-glamspot-neutral-900">Popular destinations</div>
              {['Downtown', 'Mission District', 'Marina', 'SoMa', 'Castro District'].map((location) => (
                <button
                  key={location}
                  onClick={() => handleLocationChange(location)}
                  className="block w-full text-left px-3 py-2 text-sm text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 rounded-md transition-colors"
                >
                  {location}
                </button>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <div className="w-px h-8 bg-glamspot-neutral-200" />

      {/* Check-in */}
      <Popover open={isCheckInOpen} onOpenChange={setIsCheckInOpen}>
        <PopoverTrigger asChild>
          <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 transition-colors">
            <div className="text-sm font-medium">
              {checkInDate ? format(checkInDate, 'MMM dd') : "Check in"}
            </div>
            <div className="text-xs text-glamspot-neutral-500">Add dates</div>
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={checkInDate}
            onSelect={handleCheckInSelect}
            disabled={(date) => date < new Date()}
            initialFocus
          />
        </PopoverContent>
      </Popover>

      <div className="w-px h-8 bg-glamspot-neutral-200" />

      {/* Check-out */}
      <Popover open={isCheckOutOpen} onOpenChange={setIsCheckOutOpen}>
        <PopoverTrigger asChild>
          <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 transition-colors">
            <div className="text-sm font-medium">
              {checkOutDate ? format(checkOutDate, 'MMM dd') : "Check out"}
            </div>
            <div className="text-xs text-glamspot-neutral-500">Add dates</div>
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={checkOutDate}
            onSelect={handleCheckOutSelect}
            disabled={(date) => date < new Date() || (checkInDate && date <= checkInDate)}
            initialFocus
          />
        </PopoverContent>
      </Popover>

      <div className="w-px h-8 bg-glamspot-neutral-200" />

      {/* Guests */}
      <Popover open={isGuestsOpen} onOpenChange={setIsGuestsOpen}>
        <PopoverTrigger asChild>
          <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-500 hover:bg-glamspot-neutral-50 transition-colors">
            <div className="text-sm font-medium">
              {filters.guests > 1 ? `${filters.guests} guests` : "1 guest"}
            </div>
            <div className="text-xs text-glamspot-neutral-500">Add guests</div>
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-glamspot-neutral-900">Guests</div>
                <div className="text-xs text-glamspot-neutral-500">How many people?</div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => handleGuestsChange(Math.max(1, filters.guests - 1))}
                  disabled={filters.guests <= 1}
                >
                  -
                </Button>
                <span className="w-8 text-center text-sm font-medium">{filters.guests}</span>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => handleGuestsChange(filters.guests + 1)}
                >
                  +
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <Button
        size="icon"
        className="m-2 rounded-full bg-glamspot-primary hover:bg-glamspot-primary-dark text-white"
        onClick={handleSearch}
      >
        <Search className="w-4 h-4" />
      </Button>
    </div>
  );
};