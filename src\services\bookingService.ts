import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Booking } from '@/types';

export class BookingService {
  private static readonly COLLECTION = 'bookings';

  // Create a new booking
  static async createBooking(bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const booking: Omit<Booking, 'id'> = {
        ...bookingData,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<Booking>(this.COLLECTION, booking);
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  // Get booking by ID
  static async getBookingById(id: string): Promise<Booking | null> {
    try {
      return await FirestoreService.getById<Booking>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting booking:', error);
      throw error;
    }
  }

  // Get bookings by salon
  static async getBookingsBySalon(salonId: string): Promise<Booking[]> {
    try {
      // Temporary workaround: Use single orderBy to avoid compound index
      const bookings = await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('salonId', '==', salonId),
        orderBy('date', 'desc')
      ]);
      // Sort by time client-side
      return bookings.sort((a, b) => {
        if (a.date === b.date) {
          return b.time.localeCompare(a.time);
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting bookings by salon:', error);
      throw error;
    }
  }

  // Get bookings by customer
  static async getBookingsByCustomer(customerId: string): Promise<Booking[]> {
    try {
      return await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('customerId', '==', customerId),
        orderBy('date', 'desc'),
        orderBy('time', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting bookings by customer:', error);
      throw error;
    }
  }

  // Get bookings by staff
  static async getBookingsByStaff(staffId: string): Promise<Booking[]> {
    try {
      return await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('staffId', '==', staffId),
        orderBy('date', 'desc'),
        orderBy('time', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting bookings by staff:', error);
      throw error;
    }
  }

  // Get bookings by date range
  static async getBookingsByDateRange(
    salonId: string,
    startDate: string,
    endDate: string
  ): Promise<Booking[]> {
    try {
      // Temporary workaround: Use single orderBy to avoid compound index
      const bookings = await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('date', '>=', startDate),
        where('date', '<=', endDate),
        orderBy('date', 'asc')
      ]);
      // Sort by time client-side
      return bookings.sort((a, b) => {
        if (a.date === b.date) {
          return a.time.localeCompare(b.time);
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting bookings by date range:', error);
      throw error;
    }
  }

  // Get bookings by status
  static async getBookingsByStatus(
    salonId: string,
    status: Booking['status']
  ): Promise<Booking[]> {
    try {
      // Temporary workaround: Use single orderBy to avoid compound index
      const bookings = await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('status', '==', status),
        orderBy('date', 'desc')
      ]);
      // Sort by time client-side
      return bookings.sort((a, b) => {
        if (a.date === b.date) {
          return b.time.localeCompare(a.time);
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting bookings by status:', error);
      throw error;
    }
  }

  // Update booking
  static async updateBooking(id: string, updates: Partial<Booking>): Promise<void> {
    try {
      await FirestoreService.update<Booking>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating booking:', error);
      throw error;
    }
  }

  // Update booking status
  static async updateBookingStatus(id: string, status: Booking['status']): Promise<void> {
    try {
      await FirestoreService.update<Booking>(this.COLLECTION, id, { status });
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  // Cancel booking
  static async cancelBooking(id: string, reason?: string): Promise<void> {
    try {
      const updates: Partial<Booking> = { 
        status: 'cancelled'
      };
      
      if (reason) {
        updates.notes = reason;
      }
      
      await FirestoreService.update<Booking>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }

  // Confirm booking
  static async confirmBooking(id: string): Promise<void> {
    try {
      await FirestoreService.update<Booking>(this.COLLECTION, id, { 
        status: 'confirmed' 
      });
    } catch (error) {
      console.error('Error confirming booking:', error);
      throw error;
    }
  }

  // Complete booking
  static async completeBooking(id: string): Promise<void> {
    try {
      await FirestoreService.update<Booking>(this.COLLECTION, id, { 
        status: 'completed' 
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      throw error;
    }
  }

  // Delete booking
  static async deleteBooking(id: string): Promise<void> {
    try {
      await FirestoreService.delete(this.COLLECTION, id);
    } catch (error) {
      console.error('Error deleting booking:', error);
      throw error;
    }
  }

  // Check if time slot is available
  static async isTimeSlotAvailable(
    salonId: string,
    staffId: string,
    date: string,
    time: string,
    duration: number,
    excludeBookingId?: string
  ): Promise<boolean> {
    try {
      const bookings = await FirestoreService.getWithQuery<Booking>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('staffId', '==', staffId),
        where('date', '==', date),
        where('status', 'in', ['pending', 'confirmed'])
      ]);

      // Filter out the booking being updated if provided
      const relevantBookings = excludeBookingId 
        ? bookings.filter(b => b.id !== excludeBookingId)
        : bookings;

      // Check for time conflicts
      const requestedStartMinutes = this.timeToMinutes(time);
      const requestedEndMinutes = requestedStartMinutes + duration;

      for (const booking of relevantBookings) {
        const bookingStartMinutes = this.timeToMinutes(booking.time);
        // Assuming we need to get service duration from somewhere
        const bookingEndMinutes = bookingStartMinutes + 60; // Default 60 minutes

        // Check for overlap
        if (
          (requestedStartMinutes < bookingEndMinutes && requestedEndMinutes > bookingStartMinutes)
        ) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error checking time slot availability:', error);
      throw error;
    }
  }

  // Get today's bookings for a salon
  static async getTodaysBookings(salonId: string): Promise<Booking[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      return await this.getBookingsByDateRange(salonId, today, today);
    } catch (error) {
      console.error('Error getting today\'s bookings:', error);
      throw error;
    }
  }

  // Get upcoming bookings for a salon
  static async getUpcomingBookings(salonId: string, days: number = 7): Promise<Booking[]> {
    try {
      const today = new Date();
      const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
      
      return await this.getBookingsByDateRange(
        salonId,
        today.toISOString().split('T')[0],
        futureDate.toISOString().split('T')[0]
      );
    } catch (error) {
      console.error('Error getting upcoming bookings:', error);
      throw error;
    }
  }

  // Get all bookings (admin only)
  static async getAllBookings(): Promise<Booking[]> {
    try {
      return await FirestoreService.getAll<Booking>(this.COLLECTION);
    } catch (error) {
      console.error('Error getting all bookings:', error);
      throw error;
    }
  }

  // Helper method to convert time string to minutes
  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Listen to booking changes for a salon
  static onSalonBookingsChange(salonId: string, callback: (bookings: Booking[]) => void): () => void {
    return FirestoreService.onCollectionChange<Booking>(this.COLLECTION, callback, [
      where('salonId', '==', salonId),
      orderBy('date', 'desc'),
      orderBy('time', 'desc')
    ]);
  }

  // Listen to booking changes for a customer
  static onCustomerBookingsChange(customerId: string, callback: (bookings: Booking[]) => void): () => void {
    return FirestoreService.onCollectionChange<Booking>(this.COLLECTION, callback, [
      where('customerId', '==', customerId),
      orderBy('date', 'desc'),
      orderBy('time', 'desc')
    ]);
  }
}
