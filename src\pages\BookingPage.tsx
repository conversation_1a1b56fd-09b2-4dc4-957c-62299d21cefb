import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Star, ChevronLeft, ChevronRight, Calendar, ArrowRight, Search, Bell, Menu } from "lucide-react";
import { Header } from "@/components/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

// Sample data - in a real app, this would come from an API
const salons = [
  {
    id: "1",
    name: "The Hair Lounge",
    location: "San Francisco",
    services: [
      { id: "haircut", name: "Haircut", price: 50 },
      { id: "manicure", name: "Manicure", price: 35 },
      { id: "facial", name: "Facial", price: 80 },
      { id: "massage", name: "Massage", price: 90 },
      { id: "waxing", name: "Waxing", price: 45 }
    ],
    stylists: [
      { id: "emily", name: "Emily Carter", specialty: "Specializes in haircuts" },
      { id: "sophia", name: "Sophia Bennett", specialty: "Expert in manicures" },
      { id: "olivia", name: "Olivia Hayes", specialty: "Facial and massage specialist" }
    ]
  }
];

const timeSlots = [
  "09:00 AM",
  "10:00 AM", 
  "11:00 AM",
  "02:00 PM",
  "03:00 PM"
];

const BookingPage = () => {
  const { id } = useParams();
  const salon = salons.find(s => s.id === id) || salons[0];
  
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedStylist, setSelectedStylist] = useState("emily");
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedTime, setSelectedTime] = useState("");

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const calculateTotal = () => {
    return selectedServices.reduce((total, serviceId) => {
      const service = salon.services.find(s => s.id === serviceId);
      return total + (service?.price || 0);
    }, 0);
  };

  return (
    <div className="min-h-screen bg-glamspot-neutral-50">
      <Header />
      
      <main className="flex-1">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="lg:col-span-2 space-y-8">
              {/* Header */}
              <div className="border-b border-glamspot-neutral-200 pb-8">
                <h1 className="text-3xl font-bold tracking-tight text-glamspot-neutral-900 sm:text-4xl">
                  Book Your Appointment
                </h1>
                <p className="mt-2 text-lg text-glamspot-neutral-500">
                  Choose your services and preferred stylist to get started.
                </p>
              </div>

              {/* Services Selection */}
              <div className="space-y-6" id="services">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">1. Select Services</h2>
                <div className="flex flex-wrap gap-3">
                  {salon.services.map((service) => (
                    <Button
                      key={service.id}
                      variant="outline"
                      onClick={() => handleServiceToggle(service.id)}
                      className={cn(
                        "rounded-full border border-glamspot-neutral-200 px-4 py-2 text-sm font-medium transition-colors",
                        selectedServices.includes(service.id)
                          ? "border-glamspot-primary bg-glamspot-primary text-white hover:bg-glamspot-primary-dark"
                          : "hover:border-glamspot-primary hover:bg-glamspot-primary hover:text-white"
                      )}
                    >
                      {service.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Stylist Selection */}
              <div className="space-y-6" id="stylist">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">2. Choose a Stylist</h2>
                <RadioGroup value={selectedStylist} onValueChange={setSelectedStylist}>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {salon.stylists.map((stylist) => (
                      <div key={stylist.id}>
                        <RadioGroupItem value={stylist.id} id={stylist.id} className="sr-only" />
                        <Label
                          htmlFor={stylist.id}
                          className={cn(
                            "flex items-center gap-4 rounded-lg border border-glamspot-neutral-200 p-4 cursor-pointer transition-colors",
                            selectedStylist === stylist.id
                              ? "border-glamspot-primary ring-2 ring-glamspot-primary"
                              : "hover:border-glamspot-neutral-300"
                          )}
                        >
                          <div className={cn(
                            "h-5 w-5 rounded-full border-2 flex items-center justify-center",
                            selectedStylist === stylist.id
                              ? "border-glamspot-primary bg-glamspot-primary"
                              : "border-glamspot-neutral-300"
                          )}>
                            {selectedStylist === stylist.id && (
                              <div className="h-2 w-2 rounded-full bg-white" />
                            )}
                          </div>
                          <div className="flex-grow">
                            <p className="font-semibold text-glamspot-neutral-900">{stylist.name}</p>
                            <p className="text-sm text-glamspot-neutral-500">{stylist.specialty}</p>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              {/* Date and Time Selection */}
              <div className="space-y-6" id="date-time">
                <h2 className="text-2xl font-bold text-glamspot-neutral-900">3. Select Date and Time</h2>

                {/* Date Selection */}
                <div className="rounded-lg border border-glamspot-neutral-200 p-4 bg-white">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !selectedDate && "text-muted-foreground"
                        )}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={selectedDate}
                        onSelect={setSelectedDate}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Time Selection */}
                <div className="mt-4">
                  <Select value={selectedTime} onValueChange={setSelectedTime}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a time slot" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeSlots.map((time) => (
                        <SelectItem key={time} value={time}>
                          {time}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Book Button */}
              <div className="flex justify-end pt-6">
                <Button
                  className="flex items-center justify-center gap-2 bg-glamspot-primary hover:bg-glamspot-primary-dark text-white font-bold px-6 py-3"
                  disabled={selectedServices.length === 0 || !selectedDate || !selectedTime}
                >
                  <span>Book Appointment</span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 rounded-lg border border-glamspot-neutral-200 bg-white shadow-lg p-6">
                <h2 className="text-2xl font-bold mb-6 text-glamspot-neutral-900">Your Appointments</h2>
                <div className="space-y-6">
                  {/* Sample existing appointments */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 pt-1">
                      <Calendar className="h-5 w-5 text-glamspot-neutral-500" />
                    </div>
                    <div className="flex-grow">
                      <p className="font-semibold text-glamspot-neutral-900">Haircut with Emily Carter</p>
                      <p className="text-sm text-glamspot-neutral-500">July 15, 2024, 2:00 PM</p>
                      <div className="mt-2 flex gap-2">
                        <button className="text-sm font-medium text-glamspot-primary hover:underline">
                          Reschedule
                        </button>
                        <span className="text-glamspot-neutral-300">|</span>
                        <button className="text-sm font-medium text-glamspot-neutral-500 hover:underline">
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>

                  <hr className="border-glamspot-neutral-200" />

                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 pt-1">
                      <Calendar className="h-5 w-5 text-glamspot-neutral-500" />
                    </div>
                    <div className="flex-grow">
                      <p className="font-semibold text-glamspot-neutral-900">Manicure with Sophia Bennett</p>
                      <p className="text-sm text-glamspot-neutral-500">June 20, 2024, 10:00 AM</p>
                      <div className="mt-2 flex gap-2">
                        <button className="text-sm font-medium text-glamspot-primary hover:underline">
                          Reschedule
                        </button>
                        <span className="text-glamspot-neutral-300">|</span>
                        <button className="text-sm font-medium text-glamspot-neutral-500 hover:underline">
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>

                  <hr className="border-glamspot-neutral-200" />

                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 pt-1">
                      <Calendar className="h-5 w-5 text-glamspot-neutral-500" />
                    </div>
                    <div className="flex-grow">
                      <p className="font-semibold text-glamspot-neutral-900">Facial with Olivia Hayes</p>
                      <p className="text-sm text-glamspot-neutral-500">May 25, 2024, 3:00 PM</p>
                      <div className="mt-2 flex gap-2">
                        <button className="text-sm font-medium text-glamspot-primary hover:underline">
                          Reschedule
                        </button>
                        <span className="text-glamspot-neutral-300">|</span>
                        <button className="text-sm font-medium text-glamspot-neutral-500 hover:underline">
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="mt-8 w-full border-glamspot-neutral-200 text-glamspot-neutral-900 hover:bg-glamspot-neutral-100"
                >
                  View All Appointments
                </Button>

                {/* Booking Summary */}
                {selectedServices.length > 0 && (
                  <div className="mt-8 pt-6 border-t border-glamspot-neutral-200">
                    <h3 className="font-semibold text-glamspot-neutral-900 mb-4">Booking Summary</h3>
                    <div className="space-y-2 text-sm">
                      {selectedServices.map((serviceId) => {
                        const service = salon.services.find(s => s.id === serviceId);
                        return service ? (
                          <div key={serviceId} className="flex justify-between text-glamspot-neutral-700">
                            <span>{service.name}</span>
                            <span>${service.price}</span>
                          </div>
                        ) : null;
                      })}
                      <div className="flex justify-between font-bold border-t border-glamspot-neutral-200 pt-2 mt-2 text-glamspot-neutral-900">
                        <span>Total</span>
                        <span>${calculateTotal()}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BookingPage;
