{"indexes": [{"collectionGroup": "salons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "salons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "salonId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "salonId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "salonId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "staff", "queryScope": "COLLECTION", "fields": [{"fieldPath": "salonId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "salonId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}