// Test script to verify Firebase migration is working
import { SalonService } from './services/salonService';
import { ServiceService } from './services/serviceService';
import { StaffService } from './services/staffService';
import { BookingService } from './services/bookingService';
import { AnalyticsService } from './services/analyticsService';

export async function testFirebaseMigration() {
  console.log('Testing Firebase migration...');
  
  try {
    // Test salon service
    console.log('Testing SalonService...');
    const salons = await SalonService.getActiveSalons();
    console.log(`✓ SalonService working - found ${salons.length} salons`);

    // Test service service
    console.log('Testing ServiceService...');
    const services = await ServiceService.getAllServices();
    console.log(`✓ ServiceService working - found ${services.length} services`);

    // Test staff service
    console.log('Testing StaffService...');
    const staff = await StaffService.getAllStaff();
    console.log(`✓ StaffService working - found ${staff.length} staff members`);

    // Test booking service
    console.log('Testing BookingService...');
    const bookings = await BookingService.getAllBookings();
    console.log(`✓ BookingService working - found ${bookings.length} bookings`);

    // Test analytics service
    console.log('Testing AnalyticsService...');
    const adminStats = await AnalyticsService.getAdminDashboardStats();
    console.log(`✓ AnalyticsService working - admin stats loaded`);
    console.log('Admin stats:', adminStats);

    console.log('🎉 All Firebase services are working correctly!');
    return true;
  } catch (error) {
    console.error('❌ Firebase migration test failed:', error);
    return false;
  }
}

// Helper function to test specific salon data
export async function testSalonData(salonId: string) {
  console.log(`Testing data for salon ${salonId}...`);
  
  try {
    const [salon, services, staff, bookings] = await Promise.all([
      SalonService.getSalonById(salonId),
      ServiceService.getServicesBySalon(salonId),
      StaffService.getStaffBySalon(salonId),
      BookingService.getBookingsBySalon(salonId)
    ]);

    console.log(`Salon: ${salon?.name || 'Not found'}`);
    console.log(`Services: ${services.length}`);
    console.log(`Staff: ${staff.length}`);
    console.log(`Bookings: ${bookings.length}`);

    return { salon, services, staff, bookings };
  } catch (error) {
    console.error(`Error testing salon ${salonId}:`, error);
    throw error;
  }
}

// Test data creation
export async function testDataCreation() {
  console.log('Testing data creation...');
  
  try {
    // This would require authentication, so we'll just test the structure
    console.log('✓ Data creation functions are available');
    console.log('Note: Actual creation requires authentication');
    return true;
  } catch (error) {
    console.error('Error testing data creation:', error);
    return false;
  }
}
