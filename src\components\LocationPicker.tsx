import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Search, 
  Navigation, 
  Check, 
  X,
  Crosshair,
  Map
} from 'lucide-react';
import { toast } from 'sonner';

interface LocationPickerProps {
  initialCoordinates?: { lat: number; lng: number };
  onLocationSelect: (coordinates: { lat: number; lng: number }, address: string) => void;
  onCancel?: () => void;
}

interface MapLocation {
  lat: number;
  lng: number;
  address: string;
  neighborhood: string;
}

// Mock San Francisco locations for demonstration
const mockLocations: MapLocation[] = [
  { lat: 37.7749, lng: -122.4194, address: "123 Market Street, San Francisco, CA", neighborhood: "Downtown" },
  { lat: 37.7599, lng: -122.4148, address: "456 Mission Street, San Francisco, CA", neighborhood: "Mission District" },
  { lat: 37.8021, lng: -122.4364, address: "789 Marina Blvd, San Francisco, CA", neighborhood: "Marina" },
  { lat: 37.7749, lng: -122.4094, address: "321 Howard Street, San Francisco, CA", neighborhood: "SoMa" },
  { lat: 37.7806, lng: -122.4644, address: "654 Geary Blvd, San Francisco, CA", neighborhood: "Richmond" },
  { lat: 37.8067, lng: -122.4103, address: "987 Columbus Ave, San Francisco, CA", neighborhood: "North Beach" },
  { lat: 37.7431, lng: -122.4697, address: "147 Irving Street, San Francisco, CA", neighborhood: "Sunset" },
  { lat: 37.7946, lng: -122.4014, address: "258 Montgomery Street, San Francisco, CA", neighborhood: "Financial District" },
];

export const LocationPicker: React.FC<LocationPickerProps> = ({
  initialCoordinates,
  onLocationSelect,
  onCancel
}) => {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [mapCenter, setMapCenter] = useState(
    initialCoordinates || { lat: 37.7749, lng: -122.4194 }
  );

  // Initialize with existing coordinates if provided
  useEffect(() => {
    if (initialCoordinates) {
      const existingLocation = mockLocations.find(
        loc => Math.abs(loc.lat - initialCoordinates.lat) < 0.001 && 
               Math.abs(loc.lng - initialCoordinates.lng) < 0.001
      );
      if (existingLocation) {
        setSelectedLocation(existingLocation);
      }
    }
  }, [initialCoordinates]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    
    // Simulate API search delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock search results
    const searchResults = mockLocations.filter(location =>
      location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.neighborhood.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (searchResults.length > 0) {
      const firstResult = searchResults[0];
      setMapCenter({ lat: firstResult.lat, lng: firstResult.lng });
      setSelectedLocation(firstResult);
      toast.success(`Found location: ${firstResult.address}`);
    } else {
      toast.error('Location not found. Please try a different search term.');
    }
    
    setIsSearching(false);
  };

  const handleMapClick = (lat: number, lng: number) => {
    // Find the closest mock location to the clicked point
    const closest = mockLocations.reduce((prev, curr) => {
      const prevDistance = Math.sqrt(
        Math.pow(prev.lat - lat, 2) + Math.pow(prev.lng - lng, 2)
      );
      const currDistance = Math.sqrt(
        Math.pow(curr.lat - lat, 2) + Math.pow(curr.lng - lng, 2)
      );
      return currDistance < prevDistance ? curr : prev;
    });

    setSelectedLocation(closest);
    setMapCenter({ lat: closest.lat, lng: closest.lng });
  };

  const handleConfirmLocation = () => {
    if (selectedLocation) {
      onLocationSelect(
        { lat: selectedLocation.lat, lng: selectedLocation.lng },
        selectedLocation.address
      );
      toast.success('Location saved successfully!');
    }
  };

  const handleUseCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          
          // For demo purposes, we'll use a mock location close to the user's position
          const mockCurrentLocation: MapLocation = {
            lat: latitude,
            lng: longitude,
            address: "Current Location (GPS)",
            neighborhood: "Current Area"
          };
          
          setSelectedLocation(mockCurrentLocation);
          setMapCenter({ lat: latitude, lng: longitude });
          toast.success('Current location detected!');
        },
        (error) => {
          toast.error('Unable to get current location. Please search manually.');
        }
      );
    } else {
      toast.error('Geolocation is not supported by this browser.');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-glamspot-primary" />
            Set Salon Location
          </CardTitle>
          <CardDescription>
            Choose your salon's exact location for customers to find you easily
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="location-search">Search for your address</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="location-search"
                  placeholder="Enter address, neighborhood, or landmark..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button 
                  onClick={handleSearch} 
                  disabled={isSearching}
                  className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
                >
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-col justify-end">
              <Button 
                variant="outline" 
                onClick={handleUseCurrentLocation}
                className="whitespace-nowrap"
              >
                <Navigation className="w-4 h-4 mr-2" />
                Use Current
              </Button>
            </div>
          </div>

          {/* Mock Map Interface */}
          <div className="relative">
            <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-green-100 rounded-lg border-2 border-dashed border-glamspot-neutral-300 relative overflow-hidden">
              {/* Map Background Pattern */}
              <div className="absolute inset-0 opacity-20">
                <svg width="100%" height="100%" viewBox="0 0 800 400">
                  <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94a3b8" strokeWidth="1"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                  {/* Mock streets */}
                  <line x1="0" y1="100" x2="800" y2="100" stroke="#64748b" strokeWidth="3" />
                  <line x1="0" y1="200" x2="800" y2="200" stroke="#64748b" strokeWidth="3" />
                  <line x1="0" y1="300" x2="800" y2="300" stroke="#64748b" strokeWidth="3" />
                  <line x1="200" y1="0" x2="200" y2="400" stroke="#64748b" strokeWidth="3" />
                  <line x1="400" y1="0" x2="400" y2="400" stroke="#64748b" strokeWidth="3" />
                  <line x1="600" y1="0" x2="600" y2="400" stroke="#64748b" strokeWidth="3" />
                </svg>
              </div>

              {/* Center Crosshair */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-glamspot-neutral-400">
                <Crosshair className="w-8 h-8" />
              </div>

              {/* Mock Location Markers */}
              {mockLocations.map((location, index) => {
                const x = ((location.lng + 122.5) / 0.1) * 100; // Mock coordinate conversion
                const y = ((37.85 - location.lat) / 0.15) * 100;
                const isSelected = selectedLocation?.lat === location.lat && selectedLocation?.lng === location.lng;
                
                return (
                  <div
                    key={index}
                    className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 ${
                      isSelected ? 'scale-125 z-20' : 'hover:scale-110 z-10'
                    }`}
                    style={{ 
                      left: `${Math.max(5, Math.min(95, x))}%`, 
                      top: `${Math.max(5, Math.min(95, y))}%` 
                    }}
                    onClick={() => handleMapClick(location.lat, location.lng)}
                  >
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      isSelected 
                        ? 'bg-glamspot-primary border-white shadow-lg' 
                        : 'bg-white border-glamspot-primary shadow-md hover:shadow-lg'
                    }`}>
                      <MapPin className={`w-4 h-4 ${isSelected ? 'text-white' : 'text-glamspot-primary'}`} />
                    </div>
                    {isSelected && (
                      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-2 min-w-48 border">
                        <p className="font-medium text-sm">{location.neighborhood}</p>
                        <p className="text-xs text-glamspot-neutral-600">{location.address}</p>
                      </div>
                    )}
                  </div>
                );
              })}

              {/* Map Instructions */}
              <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-md">
                <div className="flex items-center gap-2 text-sm text-glamspot-neutral-700">
                  <Map className="w-4 h-4" />
                  <span>Click on a marker to select location</span>
                </div>
              </div>
            </div>
          </div>

          {/* Selected Location Info */}
          {selectedLocation && (
            <Card className="border-glamspot-primary/20 bg-glamspot-primary/5">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-glamspot-primary rounded-full flex items-center justify-center">
                      <MapPin className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-glamspot-neutral-900">{selectedLocation.neighborhood}</h4>
                      <p className="text-sm text-glamspot-neutral-600">{selectedLocation.address}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-glamspot-neutral-500">
                        <span>Lat: {selectedLocation.lat.toFixed(4)}</span>
                        <span>Lng: {selectedLocation.lng.toFixed(4)}</span>
                      </div>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Selected
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            )}
            <Button 
              onClick={handleConfirmLocation}
              disabled={!selectedLocation}
              className="bg-glamspot-primary hover:bg-glamspot-primary-dark"
            >
              <Check className="w-4 h-4 mr-2" />
              Confirm Location
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
