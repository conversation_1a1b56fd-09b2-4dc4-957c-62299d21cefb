import { Star } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";

interface StudioCardProps {
  id: string;
  name: string;
  location: string;
  distance: string;
  rating: number;
  price: number;
  image: string;
}

export const StudioCard = ({ id, name, location, distance, rating, price, image }: StudioCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/salon/${id}`);
  };

  return (
    <Card 
      className="group cursor-pointer overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300"
      onClick={handleClick}
    >
      <div className="aspect-[4/3] overflow-hidden rounded-lg bg-glamspot-neutral-100">
        <img 
          src={image} 
          alt={name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      
      <div className="p-4 space-y-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-glamspot-neutral-900 truncate">{name}</h3>
            <p className="text-sm text-glamspot-neutral-500 truncate">
              {location} • {distance}
            </p>
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="text-sm font-medium text-glamspot-neutral-900">{rating}</span>
          </div>
        </div>
        
        <div className="pt-1">
          <span className="font-semibold text-glamspot-neutral-900">
            ${price} <span className="font-normal text-glamspot-neutral-500">/ hour</span>
          </span>
        </div>
      </div>
    </Card>
  );
};