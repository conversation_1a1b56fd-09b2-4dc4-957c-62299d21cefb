import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Home, Heart } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error("404 Error: User attempted to access non-existent route:", location.pathname);
  }, [location.pathname]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-glamspot-neutral-50">
      <div className="text-center max-w-md mx-auto px-6">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-glamspot-primary rounded-full flex items-center justify-center">
            <Heart className="w-8 h-8 text-white fill-current" />
          </div>
        </div>
        
        <h1 className="text-6xl font-bold text-glamspot-neutral-900 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-glamspot-neutral-700 mb-4">Page not found</h2>
        <p className="text-glamspot-neutral-500 mb-8">
          Sorry, we couldn't find the page you're looking for. Let's get you back to discovering amazing beauty services!
        </p>
        
        <Button 
          onClick={() => window.location.href = '/'}
          className="bg-glamspot-primary hover:bg-glamspot-primary-dark text-white inline-flex items-center gap-2"
        >
          <Home className="w-4 h-4" />
          Return to Home
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
