import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Star, X } from "lucide-react";

const serviceCategories = [
  'Hair Cutting',
  'Hair Coloring',
  'Hair Styling',
  'Facial Treatment',
  'Manicure',
  'Pedicure',
  'Massage',
  'Eyebrow Services',
  'Makeup',
  'Waxing',
];

const sortOptions = [
  { value: 'rating', label: 'Highest Rated' },
  { value: 'price', label: 'Price' },
  { value: 'distance', label: 'Distance' },
  { value: 'name', label: 'Name' },
];

export const FilterModal = () => {
  const {
    filters,
    setFilters,
    resetFilters,
    isFilterModalOpen,
    setIsFilterModalOpen,
  } = useSearchFilter();

  const [tempFilters, setTempFilters] = useState(filters);

  const handlePriceRangeChange = (value: number[]) => {
    setTempFilters(prev => ({ ...prev, priceRange: [value[0], value[1]] }));
  };

  const handleRatingChange = (rating: number) => {
    setTempFilters(prev => ({ ...prev, rating }));
  };

  const handleServiceToggle = (service: string) => {
    setTempFilters(prev => ({
      ...prev,
      services: prev.services.includes(service)
        ? prev.services.filter(s => s !== service)
        : [...prev.services, service]
    }));
  };

  const handleSortChange = (sortBy: string) => {
    setTempFilters(prev => ({ ...prev, sortBy: sortBy as any }));
  };

  const handleSortOrderChange = () => {
    setTempFilters(prev => ({
      ...prev,
      sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleApplyFilters = () => {
    setFilters(tempFilters);
    setIsFilterModalOpen(false);
  };

  const handleResetFilters = () => {
    resetFilters();
    setTempFilters(filters);
    setIsFilterModalOpen(false);
  };

  const handleClose = () => {
    setTempFilters(filters); // Reset temp filters to current filters
    setIsFilterModalOpen(false);
  };

  const activeFiltersCount = 
    (tempFilters.priceRange[0] > 0 || tempFilters.priceRange[1] < 200 ? 1 : 0) +
    (tempFilters.rating > 0 ? 1 : 0) +
    tempFilters.services.length;

  return (
    <Dialog open={isFilterModalOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Price Range */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Price Range</h3>
            <div className="px-3">
              <Slider
                value={tempFilters.priceRange}
                onValueChange={handlePriceRangeChange}
                max={200}
                min={0}
                step={5}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-glamspot-neutral-600 mt-2">
                <span>${tempFilters.priceRange[0]}</span>
                <span>${tempFilters.priceRange[1]}+</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Rating */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Minimum Rating</h3>
            <div className="flex gap-2">
              {[0, 3, 4, 4.5].map((rating) => (
                <Button
                  key={rating}
                  variant={tempFilters.rating === rating ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleRatingChange(rating)}
                  className="flex items-center gap-1"
                >
                  {rating === 0 ? (
                    "Any"
                  ) : (
                    <>
                      {rating}
                      <Star className="w-3 h-3 fill-current" />
                    </>
                  )}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Services</h3>
            <div className="grid grid-cols-2 gap-3">
              {serviceCategories.map((service) => (
                <div key={service} className="flex items-center space-x-2">
                  <Checkbox
                    id={service}
                    checked={tempFilters.services.includes(service)}
                    onCheckedChange={() => handleServiceToggle(service)}
                  />
                  <label
                    htmlFor={service}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {service}
                  </label>
                </div>
              ))}
            </div>
            {tempFilters.services.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {tempFilters.services.map((service) => (
                  <Badge
                    key={service}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {service}
                    <X
                      className="w-3 h-3 cursor-pointer"
                      onClick={() => handleServiceToggle(service)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* Sort Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Sort By</h3>
            <div className="flex flex-wrap gap-2">
              {sortOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={tempFilters.sortBy === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSortChange(option.value)}
                >
                  {option.label}
                  {tempFilters.sortBy === option.value && (
                    <span className="ml-1 text-xs">
                      ({tempFilters.sortOrder === 'asc' ? '↑' : '↓'})
                    </span>
                  )}
                </Button>
              ))}
            </div>
            {tempFilters.sortBy && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSortOrderChange}
                className="text-glamspot-neutral-600"
              >
                Sort {tempFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}
              </Button>
            )}
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="ghost" onClick={handleResetFilters}>
            Clear all
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleApplyFilters}>
              Apply filters
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
