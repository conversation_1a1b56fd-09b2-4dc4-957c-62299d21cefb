import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock,
  UserCheck,
  AlertCircle
} from 'lucide-react';
import { AnalyticsService } from '@/services/analyticsService';
import { SalonRequestService } from '@/services/salonRequestService';
import { useAuth } from '@/contexts/AuthContext';
import { Booking, SalonRegistrationRequest } from '@/types';
import { toast } from '@/components/ui/sonner';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalSalons: 0,
    totalBookings: 0,
    totalRevenue: 0,
    pendingRequests: 0,
    activeStaff: 0,
    todayBookings: 0,
  });
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [pendingRequests, setPendingRequests] = useState<SalonRegistrationRequest[]>([]);
  const [loading, setLoading] = useState(true);

  // Load dashboard data from Firebase
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);

        // Load stats, recent bookings, and pending requests
        const [statsData, bookingsData, requestsData] = await Promise.all([
          AnalyticsService.getAdminDashboardStats(),
          AnalyticsService.getRecentBookings(undefined, 5),
          SalonRequestService.getPendingRequests()
        ]);

        setStats(statsData);
        setRecentBookings(bookingsData);
        setPendingRequests(requestsData);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-glamspot-neutral-900">Dashboard</h1>
        <p className="text-glamspot-neutral-600 mt-2">
          Welcome back! Here's what's happening with your platform today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Salons
            </CardTitle>
            <Building2 className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.totalSalons}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +2 this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Bookings
            </CardTitle>
            <Calendar className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.totalBookings}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">
              ${stats.totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Pending Requests
            </CardTitle>
            <UserCheck className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{stats.pendingRequests}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">
              <AlertCircle className="h-3 w-3 mr-1" />
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-glamspot-primary" />
              Recent Bookings
            </CardTitle>
            <CardDescription>
              Latest booking activity across all salons
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 bg-glamspot-neutral-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-glamspot-neutral-900">{booking.customerName}</p>
                    <p className="text-sm text-glamspot-neutral-600">{booking.salonName}</p>
                    <p className="text-sm text-glamspot-neutral-500">{booking.service}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-glamspot-neutral-900">{booking.time}</p>
                    <Badge 
                      variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                      className="mt-1"
                    >
                      {booking.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pending Salon Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5 text-orange-500" />
              Pending Salon Requests
            </CardTitle>
            <CardDescription>
              New salon registration requests awaiting approval
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex-1">
                    <p className="font-medium text-glamspot-neutral-900">{request.salonName}</p>
                    <p className="text-sm text-glamspot-neutral-600">Owner: {request.ownerName}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-glamspot-neutral-500">{request.submittedAt}</p>
                    <Badge variant="outline" className="mt-1 border-orange-300 text-orange-700">
                      Pending
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
