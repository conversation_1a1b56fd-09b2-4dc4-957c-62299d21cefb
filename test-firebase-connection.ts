import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator, collection, getDocs } from 'firebase/firestore';

/**
 * Test script to verify Firebase connection and configuration
 * Run this to diagnose Firebase connectivity issues
 */

const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

const testFirebaseConnection = async () => {
  console.log('🔥 Testing Firebase Connection...\n');

  // Check environment variables
  console.log('📋 Checking Environment Variables:');
  const requiredEnvVars = [
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_PROJECT_ID',
    'VITE_FIREBASE_STORAGE_BUCKET',
    'VITE_FIREBASE_MESSAGING_SENDER_ID',
    'VITE_FIREBASE_APP_ID'
  ];

  let envVarsValid = true;
  requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      envVarsValid = false;
    }
  });

  if (!envVarsValid) {
    console.log('\n❌ Some environment variables are missing. Check your .env file.');
    return;
  }

  try {
    // Initialize Firebase
    console.log('\n🚀 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    console.log('✅ Firebase app initialized successfully');

    // Test Authentication
    console.log('\n🔐 Testing Authentication...');
    const auth = getAuth(app);
    console.log(`✅ Auth initialized for project: ${auth.app.options.projectId}`);

    // Test Firestore
    console.log('\n📊 Testing Firestore...');
    const db = getFirestore(app);
    console.log(`✅ Firestore initialized for project: ${db.app.options.projectId}`);

    // Test basic Firestore read
    console.log('\n📖 Testing Firestore read access...');
    try {
      const testCollection = collection(db, 'test');
      const snapshot = await getDocs(testCollection);
      console.log(`✅ Firestore read test successful (${snapshot.size} documents in test collection)`);
    } catch (firestoreError: any) {
      if (firestoreError.code === 'permission-denied') {
        console.log('⚠️  Firestore read test failed due to security rules (this is normal)');
      } else {
        console.log(`❌ Firestore read test failed: ${firestoreError.message}`);
      }
    }

    // Test specific collections
    console.log('\n🏪 Testing salon collections...');
    const collections = ['salons', 'services', 'staff', 'bookings', 'users'];
    
    for (const collectionName of collections) {
      try {
        const testCollection = collection(db, collectionName);
        const snapshot = await getDocs(testCollection);
        console.log(`✅ ${collectionName}: ${snapshot.size} documents`);
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          console.log(`⚠️  ${collectionName}: Permission denied (check security rules)`);
        } else if (error.code === 'failed-precondition') {
          console.log(`⚠️  ${collectionName}: Index required (this is expected)`);
        } else {
          console.log(`❌ ${collectionName}: ${error.message}`);
        }
      }
    }

    console.log('\n✅ Firebase connection test completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Create required Firestore indexes (see FIREBASE_SETUP.md)');
    console.log('2. Set up security rules');
    console.log('3. Create test user accounts');
    console.log('4. Add sample data');

  } catch (error: any) {
    console.log(`\n❌ Firebase connection failed: ${error.message}`);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check your .env file has correct Firebase configuration');
    console.log('2. Verify your Firebase project exists and is active');
    console.log('3. Ensure Firestore is enabled for your project');
    console.log('4. Check your internet connection');
  }
};

// Export for use in other scripts
export { testFirebaseConnection };

// Run if called directly
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  testFirebaseConnection();
}
