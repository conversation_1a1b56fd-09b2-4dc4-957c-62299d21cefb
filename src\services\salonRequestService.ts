import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { SalonRegistrationRequest } from '@/types';

export class SalonRequestService {
  private static readonly COLLECTION = 'salon_registration_requests';

  // Create a new salon registration request
  static async createSalonRequest(
    requestData: Omit<SalonRegistrationRequest, 'id' | 'submittedAt' | 'status'>
  ): Promise<string> {
    try {
      const request: Omit<SalonRegistrationRequest, 'id'> = {
        ...requestData,
        status: 'pending',
        submittedAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<SalonRegistrationRequest>(this.COLLECTION, request);
    } catch (error) {
      console.error('Error creating salon request:', error);
      throw error;
    }
  }

  // Get salon request by ID
  static async getSalonRequestById(id: string): Promise<SalonRegistrationRequest | null> {
    try {
      return await FirestoreService.getById<SalonRegistrationRequest>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting salon request:', error);
      throw error;
    }
  }

  // Get all salon requests
  static async getAllSalonRequests(): Promise<SalonRegistrationRequest[]> {
    try {
      return await FirestoreService.getWithQuery<SalonRegistrationRequest>(this.COLLECTION, [
        orderBy('submittedAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting all salon requests:', error);
      throw error;
    }
  }

  // Get salon requests by status
  static async getSalonRequestsByStatus(
    status: SalonRegistrationRequest['status']
  ): Promise<SalonRegistrationRequest[]> {
    try {
      return await FirestoreService.getWithQuery<SalonRegistrationRequest>(this.COLLECTION, [
        where('status', '==', status),
        orderBy('submittedAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting salon requests by status:', error);
      throw error;
    }
  }

  // Get pending salon requests
  static async getPendingSalonRequests(): Promise<SalonRegistrationRequest[]> {
    try {
      return await this.getSalonRequestsByStatus('pending');
    } catch (error) {
      console.error('Error getting pending salon requests:', error);
      throw error;
    }
  }

  // Get approved salon requests
  static async getApprovedSalonRequests(): Promise<SalonRegistrationRequest[]> {
    try {
      return await this.getSalonRequestsByStatus('approved');
    } catch (error) {
      console.error('Error getting approved salon requests:', error);
      throw error;
    }
  }

  // Get rejected salon requests
  static async getRejectedSalonRequests(): Promise<SalonRegistrationRequest[]> {
    try {
      return await this.getSalonRequestsByStatus('rejected');
    } catch (error) {
      console.error('Error getting rejected salon requests:', error);
      throw error;
    }
  }

  // Approve salon request
  static async approveSalonRequest(
    id: string,
    reviewedBy: string
  ): Promise<void> {
    try {
      await FirestoreService.update<SalonRegistrationRequest>(this.COLLECTION, id, {
        status: 'approved',
        reviewedAt: new Date().toISOString(),
        reviewedBy
      });
    } catch (error) {
      console.error('Error approving salon request:', error);
      throw error;
    }
  }

  // Reject salon request
  static async rejectSalonRequest(
    id: string,
    reviewedBy: string,
    rejectionReason: string
  ): Promise<void> {
    try {
      await FirestoreService.update<SalonRegistrationRequest>(this.COLLECTION, id, {
        status: 'rejected',
        reviewedAt: new Date().toISOString(),
        reviewedBy,
        rejectionReason
      });
    } catch (error) {
      console.error('Error rejecting salon request:', error);
      throw error;
    }
  }

  // Update salon request
  static async updateSalonRequest(
    id: string,
    updates: Partial<SalonRegistrationRequest>
  ): Promise<void> {
    try {
      await FirestoreService.update<SalonRegistrationRequest>(this.COLLECTION, id, updates);
    } catch (error) {
      console.error('Error updating salon request:', error);
      throw error;
    }
  }

  // Delete salon request
  static async deleteSalonRequest(id: string): Promise<void> {
    try {
      await FirestoreService.delete(this.COLLECTION, id);
    } catch (error) {
      console.error('Error deleting salon request:', error);
      throw error;
    }
  }

  // Search salon requests by owner name or salon name
  static async searchSalonRequests(searchTerm: string): Promise<SalonRegistrationRequest[]> {
    try {
      const allRequests = await this.getAllSalonRequests();
      return allRequests.filter(request => 
        request.ownerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.salonName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.ownerEmail.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching salon requests:', error);
      throw error;
    }
  }

  // Get salon request statistics
  static async getSalonRequestStats(): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    recentRequests: number; // Last 30 days
  }> {
    try {
      const allRequests = await this.getAllSalonRequests();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const thirtyDaysAgoString = thirtyDaysAgo.toISOString();

      const stats = {
        total: allRequests.length,
        pending: allRequests.filter(r => r.status === 'pending').length,
        approved: allRequests.filter(r => r.status === 'approved').length,
        rejected: allRequests.filter(r => r.status === 'rejected').length,
        recentRequests: allRequests.filter(r => r.submittedAt >= thirtyDaysAgoString).length
      };

      return stats;
    } catch (error) {
      console.error('Error getting salon request stats:', error);
      throw error;
    }
  }

  // Get salon requests by date range
  static async getSalonRequestsByDateRange(
    startDate: string,
    endDate: string
  ): Promise<SalonRegistrationRequest[]> {
    try {
      const allRequests = await this.getAllSalonRequests();
      return allRequests.filter(request => 
        request.submittedAt >= startDate && request.submittedAt <= endDate
      );
    } catch (error) {
      console.error('Error getting salon requests by date range:', error);
      throw error;
    }
  }

  // Get recent salon requests (last N days)
  static async getRecentSalonRequests(days: number = 7): Promise<SalonRegistrationRequest[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      const cutoffDateString = cutoffDate.toISOString();

      const allRequests = await this.getAllSalonRequests();
      return allRequests.filter(request => request.submittedAt >= cutoffDateString);
    } catch (error) {
      console.error('Error getting recent salon requests:', error);
      throw error;
    }
  }

  // Check if email already has a request
  static async hasExistingRequest(email: string): Promise<boolean> {
    try {
      const requests = await FirestoreService.getWithQuery<SalonRegistrationRequest>(
        this.COLLECTION,
        [where('ownerEmail', '==', email)]
      );
      return requests.length > 0;
    } catch (error) {
      console.error('Error checking existing request:', error);
      throw error;
    }
  }

  // Listen to salon request changes
  static onSalonRequestsChange(
    callback: (requests: SalonRegistrationRequest[]) => void
  ): () => void {
    return FirestoreService.onCollectionChange<SalonRegistrationRequest>(
      this.COLLECTION, 
      callback, 
      [orderBy('submittedAt', 'desc')]
    );
  }

  // Listen to pending salon request changes
  static onPendingSalonRequestsChange(
    callback: (requests: SalonRegistrationRequest[]) => void
  ): () => void {
    return FirestoreService.onCollectionChange<SalonRegistrationRequest>(
      this.COLLECTION, 
      callback, 
      [
        where('status', '==', 'pending'),
        orderBy('submittedAt', 'desc')
      ]
    );
  }

  // Batch process salon requests
  static async batchProcessSalonRequests(
    operations: Array<{
      id: string;
      action: 'approve' | 'reject';
      reviewedBy: string;
      rejectionReason?: string;
    }>
  ): Promise<void> {
    try {
      const batchOperations = operations.map(op => {
        const data: Partial<SalonRegistrationRequest> = {
          status: op.action === 'approve' ? 'approved' : 'rejected',
          reviewedAt: new Date().toISOString(),
          reviewedBy: op.reviewedBy
        };

        if (op.action === 'reject' && op.rejectionReason) {
          data.rejectionReason = op.rejectionReason;
        }

        return {
          type: 'update' as const,
          collection: this.COLLECTION,
          id: op.id,
          data
        };
      });

      await FirestoreService.batchWrite(batchOperations);
    } catch (error) {
      console.error('Error batch processing salon requests:', error);
      throw error;
    }
  }
}
