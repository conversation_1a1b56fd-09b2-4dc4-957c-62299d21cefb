import { where, orderBy } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Notification } from '@/types';

export class NotificationService {
  private static readonly COLLECTION = 'notifications';

  // Create a new notification
  static async createNotification(
    notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead' | 'readAt'>
  ): Promise<string> {
    try {
      const notification: Omit<Notification, 'id'> = {
        ...notificationData,
        isRead: false,
        createdAt: new Date().toISOString()
      };
      
      return await FirestoreService.create<Notification>(this.COLLECTION, notification);
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Get notification by ID
  static async getNotificationById(id: string): Promise<Notification | null> {
    try {
      return await FirestoreService.getById<Notification>(this.COLLECTION, id);
    } catch (error) {
      console.error('Error getting notification:', error);
      throw error;
    }
  }

  // Get notifications by salon
  static async getNotificationsBySalon(salonId: string): Promise<Notification[]> {
    try {
      return await FirestoreService.getWithQuery<Notification>(this.COLLECTION, [
        where('salonId', '==', salonId),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting notifications by salon:', error);
      throw error;
    }
  }

  // Get unread notifications by salon
  static async getUnreadNotificationsBySalon(salonId: string): Promise<Notification[]> {
    try {
      return await FirestoreService.getWithQuery<Notification>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('isRead', '==', false),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting unread notifications by salon:', error);
      throw error;
    }
  }

  // Get notifications by type
  static async getNotificationsByType(
    salonId: string,
    type: Notification['type']
  ): Promise<Notification[]> {
    try {
      return await FirestoreService.getWithQuery<Notification>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('type', '==', type),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting notifications by type:', error);
      throw error;
    }
  }

  // Get notifications by priority
  static async getNotificationsByPriority(
    salonId: string,
    priority: Notification['priority']
  ): Promise<Notification[]> {
    try {
      return await FirestoreService.getWithQuery<Notification>(this.COLLECTION, [
        where('salonId', '==', salonId),
        where('priority', '==', priority),
        orderBy('createdAt', 'desc')
      ]);
    } catch (error) {
      console.error('Error getting notifications by priority:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markAsRead(id: string): Promise<void> {
    try {
      await FirestoreService.update<Notification>(this.COLLECTION, id, {
        isRead: true,
        isNew: false,
        readAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read for a salon
  static async markAllAsRead(salonId: string): Promise<void> {
    try {
      const unreadNotifications = await this.getUnreadNotificationsBySalon(salonId);
      
      const operations = unreadNotifications.map(notification => ({
        type: 'update' as const,
        collection: this.COLLECTION,
        id: notification.id,
        data: {
          isRead: true,
          isNew: false,
          readAt: new Date().toISOString()
        }
      }));
      
      if (operations.length > 0) {
        await FirestoreService.batchWrite(operations);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Delete notification
  static async deleteNotification(id: string): Promise<void> {
    try {
      await FirestoreService.delete(this.COLLECTION, id);
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Delete old notifications (older than specified days)
  static async deleteOldNotifications(salonId: string, daysOld: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      const cutoffDateString = cutoffDate.toISOString();

      const allNotifications = await this.getNotificationsBySalon(salonId);
      const oldNotifications = allNotifications.filter(
        notification => notification.createdAt < cutoffDateString
      );

      const operations = oldNotifications.map(notification => ({
        type: 'delete' as const,
        collection: this.COLLECTION,
        id: notification.id
      }));

      if (operations.length > 0) {
        await FirestoreService.batchWrite(operations);
      }
    } catch (error) {
      console.error('Error deleting old notifications:', error);
      throw error;
    }
  }

  // Get notification count by salon
  static async getNotificationCount(salonId: string): Promise<{
    total: number;
    unread: number;
    byType: Record<Notification['type'], number>;
    byPriority: Record<Notification['priority'], number>;
  }> {
    try {
      const notifications = await this.getNotificationsBySalon(salonId);
      
      const count = {
        total: notifications.length,
        unread: notifications.filter(n => !n.isRead).length,
        byType: {
          booking: 0,
          review: 0,
          staff: 0,
          payment: 0,
          system: 0
        } as Record<Notification['type'], number>,
        byPriority: {
          low: 0,
          medium: 0,
          high: 0
        } as Record<Notification['priority'], number>
      };

      notifications.forEach(notification => {
        count.byType[notification.type]++;
        count.byPriority[notification.priority]++;
      });

      return count;
    } catch (error) {
      console.error('Error getting notification count:', error);
      throw error;
    }
  }

  // Create booking notification
  static async createBookingNotification(
    salonId: string,
    bookingId: string,
    type: 'new_booking' | 'booking_cancelled' | 'booking_updated',
    customerName: string
  ): Promise<string> {
    try {
      const messages = {
        new_booking: `New booking request from ${customerName}`,
        booking_cancelled: `Booking cancelled by ${customerName}`,
        booking_updated: `Booking updated by ${customerName}`
      };

      const titles = {
        new_booking: 'New Booking Request',
        booking_cancelled: 'Booking Cancelled',
        booking_updated: 'Booking Updated'
      };

      return await this.createNotification({
        salonId,
        type: 'booking',
        title: titles[type],
        message: messages[type],
        isNew: true,
        priority: type === 'booking_cancelled' ? 'high' : 'medium',
        relatedId: bookingId
      });
    } catch (error) {
      console.error('Error creating booking notification:', error);
      throw error;
    }
  }

  // Create system notification
  static async createSystemNotification(
    salonId: string,
    title: string,
    message: string,
    priority: Notification['priority'] = 'medium'
  ): Promise<string> {
    try {
      return await this.createNotification({
        salonId,
        type: 'system',
        title,
        message,
        isNew: true,
        priority
      });
    } catch (error) {
      console.error('Error creating system notification:', error);
      throw error;
    }
  }

  // Listen to notification changes for a salon
  static onSalonNotificationsChange(
    salonId: string, 
    callback: (notifications: Notification[]) => void
  ): () => void {
    return FirestoreService.onCollectionChange<Notification>(this.COLLECTION, callback, [
      where('salonId', '==', salonId),
      orderBy('createdAt', 'desc')
    ]);
  }

  // Listen to unread notification changes for a salon
  static onUnreadNotificationsChange(
    salonId: string, 
    callback: (notifications: Notification[]) => void
  ): () => void {
    return FirestoreService.onCollectionChange<Notification>(this.COLLECTION, callback, [
      where('salonId', '==', salonId),
      where('isRead', '==', false),
      orderBy('createdAt', 'desc')
    ]);
  }
}
