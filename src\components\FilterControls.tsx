import { Button } from "@/components/ui/button";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { SlidersHorizontal, Map, Grid3X3 } from "lucide-react";

export const FilterControls = () => {
  const {
    viewMode,
    setViewMode,
    isFilterModalOpen,
    setIsFilterModalOpen,
    filteredSalons
  } = useSearchFilter();

  const handleFilterClick = () => {
    setIsFilterModalOpen(true);
  };

  const handleMapToggle = () => {
    setViewMode(viewMode === 'grid' ? 'map' : 'grid');
  };

  return (
    <div className="flex items-center gap-4">
      <div className="text-sm text-glamspot-neutral-600">
        {filteredSalons.length} {filteredSalons.length === 1 ? 'place' : 'places'}
      </div>

      <Button
        variant="outline"
        className="flex items-center gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50"
        onClick={handleFilterClick}
      >
        <SlidersHorizontal className="w-4 h-4" />
        Filters
      </Button>

      <Button
        variant="outline"
        className={`flex items-center gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 ${
          viewMode === 'map' ? 'bg-glamspot-neutral-100' : ''
        }`}
        onClick={handleMapToggle}
      >
        {viewMode === 'grid' ? (
          <>
            <Map className="w-4 h-4" />
            Show map
          </>
        ) : (
          <>
            <Grid3X3 className="w-4 h-4" />
            Show list
          </>
        )}
      </Button>
    </div>
  );
};