import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Notification } from '@/types';
import { useAuth } from './AuthContext';
import { toast } from 'sonner';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

// Mock notifications for development
const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    salonId: '1',
    type: 'booking',
    title: 'New Booking Request',
    message: '<PERSON> has requested a haircut appointment for tomorrow at 2:00 PM',
    isRead: false,
    isNew: true,
    priority: 'high',
    relatedId: 'booking-123',
    createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
  },
  {
    id: 'notif-2',
    salonId: '1',
    type: 'review',
    title: 'New 5-Star Review',
    message: 'Mike Chen left a 5-star review: "Amazing service and great atmosphere!"',
    isRead: false,
    isNew: true,
    priority: 'medium',
    relatedId: 'review-456',
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
  },
  {
    id: 'notif-3',
    salonId: '1',
    type: 'staff',
    title: 'Schedule Update',
    message: 'Emily Carter has updated her availability for next week',
    isRead: true,
    isNew: false,
    priority: 'low',
    relatedId: 'staff-789',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
  },
  {
    id: 'notif-4',
    salonId: '1',
    type: 'payment',
    title: 'Payment Received',
    message: 'Payment of $150 received for booking #B-2024-001',
    isRead: true,
    isNew: false,
    priority: 'medium',
    relatedId: 'payment-101',
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
  },
];

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, isSalonOwner } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Initialize notifications for salon owners
  useEffect(() => {
    if (isSalonOwner && user) {
      // Filter notifications for the current salon owner's salon
      const salonNotifications = mockNotifications.filter(
        notif => notif.salonId === (user as any).salonId
      );
      setNotifications(salonNotifications);
    }
  }, [user, isSalonOwner]);

  // Simulate real-time notifications
  useEffect(() => {
    if (!isSalonOwner) return;

    const interval = setInterval(() => {
      // Simulate receiving a new booking notification
      if (Math.random() < 0.1) { // 10% chance every 30 seconds
        const newNotification: Notification = {
          id: `notif-${Date.now()}`,
          salonId: (user as any)?.salonId || '1',
          type: 'booking',
          title: 'New Booking Request',
          message: `New booking request received at ${new Date().toLocaleTimeString()}`,
          isRead: false,
          isNew: true,
          priority: 'high',
          createdAt: new Date().toISOString(),
        };
        
        setNotifications(prev => [newNotification, ...prev]);
        
        // Show toast notification
        toast.success('New booking request received!', {
          description: newNotification.message,
          action: {
            label: 'View',
            onClick: () => {
              // Navigate to bookings page
              window.location.href = '/salon-owner/bookings';
            },
          },
        });
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [isSalonOwner, user]);

  const unreadCount = notifications.filter(notif => !notif.isRead).length;

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: `notif-${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    
    setNotifications(prev => [newNotification, ...prev]);
    
    // Show toast for high priority notifications
    if (newNotification.priority === 'high') {
      toast.success(newNotification.title, {
        description: newNotification.message,
      });
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId
          ? { ...notif, isRead: true, isNew: false, readAt: new Date().toISOString() }
          : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({
        ...notif,
        isRead: true,
        isNew: false,
        readAt: new Date().toISOString(),
      }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
