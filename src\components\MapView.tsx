import { useState } from "react";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Heart, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

// Mock map coordinates for San Francisco areas
const locationCoordinates: Record<string, { lat: number; lng: number }> = {
  'Downtown': { lat: 37.7749, lng: -122.4194 },
  'Mission District': { lat: 37.7599, lng: -122.4148 },
  'Marina': { lat: 37.8021, lng: -122.4364 },
  'SoMa': { lat: 37.7749, lng: -122.4094 },
  'Castro District': { lat: 37.7609, lng: -122.4350 },
  'Richmond': { lat: 37.7806, lng: -122.4644 },
  'North Beach': { lat: 37.8067, lng: -122.4103 },
  'Sunset': { lat: 37.7431, lng: -122.4697 },
  'Financial District': { lat: 37.7946, lng: -122.4014 },
};

export const MapView = () => {
  const { filteredSalons } = useSearchFilter();
  const [selectedSalon, setSelectedSalon] = useState<string | null>(null);

  // Convert salon data to include coordinates
  const salonsWithCoords = filteredSalons.map(salon => ({
    ...salon,
    coordinates: salon.coordinates || locationCoordinates[salon.location] || locationCoordinates['Downtown'],
    price: Math.round(salon.rating * 20), // Mock price calculation
  }));

  // Calculate map bounds to fit all salons
  const bounds = salonsWithCoords.reduce(
    (acc, salon) => ({
      minLat: Math.min(acc.minLat, salon.coordinates.lat),
      maxLat: Math.max(acc.maxLat, salon.coordinates.lat),
      minLng: Math.min(acc.minLng, salon.coordinates.lng),
      maxLng: Math.max(acc.maxLng, salon.coordinates.lng),
    }),
    { minLat: Infinity, maxLat: -Infinity, minLng: Infinity, maxLng: -Infinity }
  );

  const handleSalonClick = (salonId: string) => {
    setSelectedSalon(selectedSalon === salonId ? null : salonId);
  };

  return (
    <div className="h-[600px] bg-glamspot-neutral-100 rounded-lg relative overflow-hidden">
      {/* Mock Map Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100%" viewBox="0 0 800 600">
            {/* Mock street lines */}
            <g stroke="#94a3b8" strokeWidth="2" fill="none">
              <line x1="0" y1="150" x2="800" y2="150" />
              <line x1="0" y1="300" x2="800" y2="300" />
              <line x1="0" y1="450" x2="800" y2="450" />
              <line x1="200" y1="0" x2="200" y2="600" />
              <line x1="400" y1="0" x2="400" y2="600" />
              <line x1="600" y1="0" x2="600" y2="600" />
            </g>
          </svg>
        </div>
      </div>

      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <Button size="sm" variant="outline" className="bg-white">
          +
        </Button>
        <Button size="sm" variant="outline" className="bg-white">
          -
        </Button>
      </div>

      {/* Salon Markers */}
      {salonsWithCoords.map((salon, index) => {
        const x = 100 + (index % 6) * 120; // Distribute horizontally
        const y = 100 + Math.floor(index / 6) * 150; // Distribute vertically
        const isSelected = selectedSalon === salon.id;

        return (
          <div
            key={salon.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer z-20"
            style={{ left: `${x}px`, top: `${y}px` }}
            onClick={() => handleSalonClick(salon.id)}
          >
            {/* Marker */}
            <div
              className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                isSelected
                  ? 'bg-glamspot-primary border-white scale-110 shadow-lg'
                  : 'bg-white border-glamspot-primary hover:scale-105 shadow-md'
              }`}
            >
              <MapPin className={`w-5 h-5 ${isSelected ? 'text-white' : 'text-glamspot-primary'}`} />
              
              {/* Price Badge */}
              <div className="absolute -top-2 -right-2">
                <Badge 
                  variant="secondary" 
                  className="text-xs px-1 py-0 bg-white border border-glamspot-neutral-200"
                >
                  ${salon.price}
                </Badge>
              </div>
            </div>

            {/* Salon Info Card */}
            {isSelected && (
              <Card className="absolute top-12 left-1/2 transform -translate-x-1/2 w-64 shadow-lg z-30">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold text-sm">{salon.name}</h3>
                        <p className="text-xs text-glamspot-neutral-600">{salon.location}</p>
                      </div>
                      <Button size="sm" variant="ghost" className="p-1">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 fill-current text-yellow-400" />
                        <span className="text-xs font-medium">{salon.rating}</span>
                      </div>
                      <span className="text-xs text-glamspot-neutral-500">
                        ({salon.reviews} reviews)
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-semibold">${salon.price}</span>
                        <span className="text-xs text-glamspot-neutral-600"> / session</span>
                      </div>
                      <Button size="sm" asChild>
                        <Link to={`/salon/${salon.id}`}>
                          <ExternalLink className="w-3 h-3 mr-1" />
                          View
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );
      })}

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-md">
        <div className="text-xs font-medium text-glamspot-neutral-900 mb-2">
          {filteredSalons.length} salon{filteredSalons.length !== 1 ? 's' : ''} found
        </div>
        <div className="flex items-center gap-2 text-xs text-glamspot-neutral-600">
          <div className="w-3 h-3 bg-glamspot-primary rounded-full"></div>
          <span>Beauty salons</span>
        </div>
      </div>

      {/* No results message */}
      {filteredSalons.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Card className="p-6 text-center">
            <MapPin className="w-12 h-12 text-glamspot-neutral-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-glamspot-neutral-900 mb-2">
              No salons found
            </h3>
            <p className="text-sm text-glamspot-neutral-600">
              Try adjusting your search criteria or filters
            </p>
          </Card>
        </div>
      )}
    </div>
  );
};
