import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DollarSign, 
  Plus, 
  Edit, 
  Trash2, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Percent,
  Tag
} from 'lucide-react';
import { toast } from 'sonner';

interface PricingRule {
  id: string;
  name: string;
  type: 'discount' | 'surcharge';
  value: number;
  valueType: 'percentage' | 'fixed';
  condition: string;
  isActive: boolean;
}

interface ServicePricing {
  id: string;
  serviceId: string;
  serviceName: string;
  basePrice: number;
  currentPrice: number;
  category: string;
  isActive: boolean;
}

const SalonOwnerPricing = () => {
  const [isAddRuleDialogOpen, setIsAddRuleDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<PricingRule | null>(null);
  
  // Mock pricing rules
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([
    {
      id: 'rule-1',
      name: 'Senior Discount',
      type: 'discount',
      value: 15,
      valueType: 'percentage',
      condition: 'Age 65+',
      isActive: true,
    },
    {
      id: 'rule-2',
      name: 'Student Discount',
      type: 'discount',
      value: 10,
      valueType: 'percentage',
      condition: 'Valid student ID',
      isActive: true,
    },
    {
      id: 'rule-3',
      name: 'Weekend Surcharge',
      type: 'surcharge',
      value: 20,
      valueType: 'fixed',
      condition: 'Saturday & Sunday',
      isActive: false,
    },
  ]);

  // Mock service pricing
  const [servicePricing, setServicePricing] = useState<ServicePricing[]>([
    {
      id: 'pricing-1',
      serviceId: 'service-1',
      serviceName: 'Haircut & Style',
      basePrice: 85,
      currentPrice: 85,
      category: 'Hair',
      isActive: true,
    },
    {
      id: 'pricing-2',
      serviceId: 'service-2',
      serviceName: 'Hair Coloring',
      basePrice: 150,
      currentPrice: 150,
      category: 'Hair',
      isActive: true,
    },
    {
      id: 'pricing-3',
      serviceId: 'service-3',
      serviceName: 'Highlights',
      basePrice: 120,
      currentPrice: 120,
      category: 'Hair',
      isActive: true,
    },
  ]);

  const [ruleFormData, setRuleFormData] = useState({
    name: '',
    type: 'discount' as 'discount' | 'surcharge',
    value: '',
    valueType: 'percentage' as 'percentage' | 'fixed',
    condition: '',
  });

  const handleAddRule = () => {
    setRuleFormData({
      name: '',
      type: 'discount',
      value: '',
      valueType: 'percentage',
      condition: '',
    });
    setEditingRule(null);
    setIsAddRuleDialogOpen(true);
  };

  const handleEditRule = (rule: PricingRule) => {
    setRuleFormData({
      name: rule.name,
      type: rule.type,
      value: rule.value.toString(),
      valueType: rule.valueType,
      condition: rule.condition,
    });
    setEditingRule(rule);
    setIsAddRuleDialogOpen(true);
  };

  const handleSubmitRule = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!ruleFormData.name || !ruleFormData.value || !ruleFormData.condition) {
      toast.error('Please fill in all required fields');
      return;
    }

    const newRule: PricingRule = {
      id: editingRule ? editingRule.id : `rule-${Date.now()}`,
      name: ruleFormData.name,
      type: ruleFormData.type,
      value: parseFloat(ruleFormData.value),
      valueType: ruleFormData.valueType,
      condition: ruleFormData.condition,
      isActive: true,
    };

    if (editingRule) {
      setPricingRules(prev => prev.map(rule => rule.id === editingRule.id ? newRule : rule));
      toast.success('Pricing rule updated successfully!');
    } else {
      setPricingRules(prev => [...prev, newRule]);
      toast.success('Pricing rule added successfully!');
    }

    setIsAddRuleDialogOpen(false);
    setEditingRule(null);
  };

  const handleDeleteRule = (ruleId: string) => {
    setPricingRules(prev => prev.filter(rule => rule.id !== ruleId));
    toast.success('Pricing rule deleted successfully!');
  };

  const handleToggleRule = (ruleId: string) => {
    setPricingRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    ));
  };

  const handlePriceUpdate = (pricingId: string, newPrice: number) => {
    setServicePricing(prev => prev.map(pricing => 
      pricing.id === pricingId ? { ...pricing, currentPrice: newPrice } : pricing
    ));
    toast.success('Price updated successfully!');
  };

  const totalRevenue = servicePricing.reduce((sum, pricing) => sum + (pricing.currentPrice * 10), 0); // Mock calculation
  const averagePrice = servicePricing.reduce((sum, pricing) => sum + pricing.currentPrice, 0) / servicePricing.length;
  const activeRules = pricingRules.filter(rule => rule.isActive).length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Pricing Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage your service pricing and special offers
          </p>
        </div>
        <Button onClick={handleAddRule} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
          <Plus className="w-4 h-4 mr-2" />
          Add Pricing Rule
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Services
            </CardTitle>
            <DollarSign className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{servicePricing.length}</div>
            <p className="text-xs text-green-600 mt-1">
              {servicePricing.filter(s => s.isActive).length} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Average Price
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">${averagePrice.toFixed(0)}</div>
            <p className="text-xs text-green-600 mt-1">
              +5% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Active Rules
            </CardTitle>
            <Tag className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{activeRules}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              Pricing rules active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Monthly Revenue
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600 mt-1">
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Service Pricing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-glamspot-primary" />
              Service Pricing
            </CardTitle>
            <CardDescription>
              Manage individual service prices
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service</TableHead>
                  <TableHead>Base Price</TableHead>
                  <TableHead>Current Price</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {servicePricing.map((pricing) => (
                  <TableRow key={pricing.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{pricing.serviceName}</p>
                        <p className="text-sm text-glamspot-neutral-500">{pricing.category}</p>
                      </div>
                    </TableCell>
                    <TableCell>${pricing.basePrice}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">${pricing.currentPrice}</span>
                        {pricing.currentPrice !== pricing.basePrice && (
                          <Badge variant="outline" className="text-xs">
                            {pricing.currentPrice > pricing.basePrice ? '+' : ''}
                            {((pricing.currentPrice - pricing.basePrice) / pricing.basePrice * 100).toFixed(0)}%
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pricing Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="w-5 h-5 text-glamspot-primary" />
              Pricing Rules
            </CardTitle>
            <CardDescription>
              Special discounts and surcharges
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pricingRules.map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={() => handleToggleRule(rule.id)}
                    />
                    <div>
                      <p className="font-medium">{rule.name}</p>
                      <p className="text-sm text-glamspot-neutral-500">{rule.condition}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={rule.type === 'discount' ? 'default' : 'destructive'}>
                      {rule.type === 'discount' ? (
                        <TrendingDown className="w-3 h-3 mr-1" />
                      ) : (
                        <TrendingUp className="w-3 h-3 mr-1" />
                      )}
                      {rule.valueType === 'percentage' ? `${rule.value}%` : `$${rule.value}`}
                    </Badge>
                    <Button variant="ghost" size="sm" onClick={() => handleEditRule(rule)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteRule(rule.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add/Edit Pricing Rule Dialog */}
      <Dialog open={isAddRuleDialogOpen} onOpenChange={setIsAddRuleDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingRule ? 'Edit' : 'Add'} Pricing Rule</DialogTitle>
            <DialogDescription>
              {editingRule ? 'Update the pricing rule details' : 'Create a new pricing rule for your salon'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRule} className="space-y-4">
            <div>
              <Label htmlFor="ruleName">Rule Name</Label>
              <Input
                id="ruleName"
                value={ruleFormData.name}
                onChange={(e) => setRuleFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Senior Discount"
                required
              />
            </div>
            <div>
              <Label htmlFor="ruleType">Type</Label>
              <Select value={ruleFormData.type} onValueChange={(value: 'discount' | 'surcharge') => 
                setRuleFormData(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="discount">Discount</SelectItem>
                  <SelectItem value="surcharge">Surcharge</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="ruleValue">Value</Label>
                <Input
                  id="ruleValue"
                  type="number"
                  value={ruleFormData.value}
                  onChange={(e) => setRuleFormData(prev => ({ ...prev, value: e.target.value }))}
                  placeholder="10"
                  required
                />
              </div>
              <div>
                <Label htmlFor="valueType">Type</Label>
                <Select value={ruleFormData.valueType} onValueChange={(value: 'percentage' | 'fixed') => 
                  setRuleFormData(prev => ({ ...prev, valueType: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">%</SelectItem>
                    <SelectItem value="fixed">$</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="condition">Condition</Label>
              <Input
                id="condition"
                value={ruleFormData.condition}
                onChange={(e) => setRuleFormData(prev => ({ ...prev, condition: e.target.value }))}
                placeholder="e.g., Age 65+"
                required
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setIsAddRuleDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
                {editingRule ? 'Update' : 'Add'} Rule
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonOwnerPricing;
