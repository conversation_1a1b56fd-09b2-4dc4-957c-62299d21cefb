import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { User, Admin, SalonOwner } from '@/types';

export class AuthService {
  // Sign in with email and password
  static async signIn(email: string, password: string): Promise<User | null> {
    try {
      // Validate input
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (userDoc.exists()) {
        return { id: firebaseUser.uid, ...userDoc.data() } as User;
      }

      // If user document doesn't exist, create a basic one
      const basicUser: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        name: firebaseUser.displayName || email.split('@')[0],
        role: 'customer',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), basicUser);
      return basicUser;
    } catch (error: any) {
      console.error('Sign in error:', error);

      // Provide more user-friendly error messages
      if (error?.code === 'auth/invalid-credential') {
        throw new Error('Invalid email or password. Please check your credentials and try again.');
      } else if (error?.code === 'auth/user-not-found') {
        throw new Error('No account found with this email address.');
      } else if (error?.code === 'auth/wrong-password') {
        throw new Error('Incorrect password. Please try again.');
      } else if (error?.code === 'auth/too-many-requests') {
        throw new Error('Too many failed attempts. Please try again later.');
      } else if (error?.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection.');
      }

      throw error;
    }
  }

  // Create new user account
  static async signUp(
    email: string, 
    password: string, 
    userData: Partial<User>
  ): Promise<User | null> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: userData.name
      });

      // Create user document in Firestore
      const newUser: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        name: userData.name!,
        role: userData.role || 'customer',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...userData
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), newUser);

      // Send email verification
      await sendEmailVerification(firebaseUser);

      return newUser;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Get current user data
  static async getCurrentUser(): Promise<User | null> {
    const firebaseUser = auth.currentUser;
    if (!firebaseUser) return null;

    try {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (userDoc.exists()) {
        return { id: firebaseUser.uid, ...userDoc.data() } as User;
      }
      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUserProfile(userId: string, updates: Partial<User>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', userId), {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Send password reset email
  static async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  static onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        callback(null);
      }
    });
  }

  // Create salon owner account
  static async createSalonOwner(
    email: string,
    password: string,
    ownerData: Partial<SalonOwner>
  ): Promise<SalonOwner | null> {
    try {
      const salonOwner: Partial<SalonOwner> = {
        ...ownerData,
        role: 'salon_owner',
        isApproved: false,
        requestedAt: new Date().toISOString()
      };

      const user = await this.signUp(email, password, salonOwner);
      return user as SalonOwner;
    } catch (error) {
      console.error('Create salon owner error:', error);
      throw error;
    }
  }

  // Approve salon owner
  static async approveSalonOwner(userId: string, approvedBy: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', userId), {
        isApproved: true,
        approvedAt: new Date().toISOString(),
        approvedBy,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Approve salon owner error:', error);
      throw error;
    }
  }
}
