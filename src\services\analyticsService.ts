import { where, orderBy, limit, startAfter, Timestamp } from 'firebase/firestore';
import { FirestoreService } from './firestoreService';
import { Booking, Salon, Service, Staff, AnalyticsData } from '@/types';

export class AnalyticsService {
  // Get dashboard statistics for admin
  static async getAdminDashboardStats(): Promise<{
    totalSalons: number;
    totalBookings: number;
    totalRevenue: number;
    pendingRequests: number;
    activeStaff: number;
    todayBookings: number;
  }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get all salons
      const salons = await FirestoreService.getAll<Salon>('salons');
      const totalSalons = salons.filter(salon => salon.isActive).length;

      // Get all bookings
      const allBookings = await FirestoreService.getAll<Booking>('bookings');
      const totalBookings = allBookings.length;
      
      // Calculate total revenue
      const totalRevenue = allBookings
        .filter(booking => booking.status === 'completed')
        .reduce((sum, booking) => sum + booking.totalAmount, 0);

      // Get pending salon requests
      const pendingRequests = await FirestoreService.getWithQuery('salon_registration_requests', [
        where('status', '==', 'pending')
      ]);

      // Get active staff
      const allStaff = await FirestoreService.getAll<Staff>('staff');
      const activeStaff = allStaff.filter(staff => staff.isActive).length;

      // Get today's bookings
      const todayBookings = allBookings.filter(booking => booking.date === today).length;

      return {
        totalSalons,
        totalBookings,
        totalRevenue,
        pendingRequests: pendingRequests.length,
        activeStaff,
        todayBookings
      };
    } catch (error) {
      console.error('Error getting admin dashboard stats:', error);
      throw error;
    }
  }

  // Get dashboard statistics for salon owner
  static async getSalonOwnerDashboardStats(salonId: string): Promise<{
    totalBookings: number;
    monthlyRevenue: number;
    totalStaff: number;
    avgRating: number;
    todayBookings: number;
    pendingBookings: number;
  }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM format

      // Get salon bookings
      const salonBookings = await FirestoreService.getWithQuery<Booking>('bookings', [
        where('salonId', '==', salonId)
      ]);

      const totalBookings = salonBookings.length;

      // Calculate monthly revenue
      const monthlyRevenue = salonBookings
        .filter(booking => 
          booking.status === 'completed' && 
          booking.date.startsWith(currentMonth)
        )
        .reduce((sum, booking) => sum + booking.totalAmount, 0);

      // Get salon staff
      const salonStaff = await FirestoreService.getWithQuery<Staff>('staff', [
        where('salonId', '==', salonId),
        where('isActive', '==', true)
      ]);
      const totalStaff = salonStaff.length;

      // Get salon data for rating
      const salon = await FirestoreService.getById<Salon>('salons', salonId);
      const avgRating = salon?.rating || 0;

      // Get today's bookings
      const todayBookings = salonBookings.filter(booking => booking.date === today).length;

      // Get pending bookings
      const pendingBookings = salonBookings.filter(booking => booking.status === 'pending').length;

      return {
        totalBookings,
        monthlyRevenue,
        totalStaff,
        avgRating,
        todayBookings,
        pendingBookings
      };
    } catch (error) {
      console.error('Error getting salon owner dashboard stats:', error);
      throw error;
    }
  }

  // Get recent bookings for dashboard
  static async getRecentBookings(salonId?: string, limitCount: number = 5): Promise<Booking[]> {
    try {
      const constraints = salonId 
        ? [where('salonId', '==', salonId), orderBy('createdAt', 'desc'), limit(limitCount)]
        : [orderBy('createdAt', 'desc'), limit(limitCount)];

      return await FirestoreService.getWithQuery<Booking>('bookings', constraints);
    } catch (error) {
      console.error('Error getting recent bookings:', error);
      throw error;
    }
  }

  // Get revenue analytics
  static async getRevenueAnalytics(salonId?: string): Promise<{
    revenueByMonth: Array<{ month: string; revenue: number }>;
    totalRevenue: number;
  }> {
    try {
      const constraints = salonId 
        ? [where('salonId', '==', salonId), where('status', '==', 'completed')]
        : [where('status', '==', 'completed')];

      const completedBookings = await FirestoreService.getWithQuery<Booking>('bookings', constraints);

      // Calculate total revenue
      const totalRevenue = completedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);

      // Group by month
      const revenueByMonth: Record<string, number> = {};
      completedBookings.forEach(booking => {
        const month = booking.date.substring(0, 7); // YYYY-MM
        revenueByMonth[month] = (revenueByMonth[month] || 0) + booking.totalAmount;
      });

      // Convert to array format
      const revenueArray = Object.entries(revenueByMonth)
        .map(([month, revenue]) => ({
          month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short' }),
          revenue
        }))
        .sort((a, b) => a.month.localeCompare(b.month));

      return {
        revenueByMonth: revenueArray,
        totalRevenue
      };
    } catch (error) {
      console.error('Error getting revenue analytics:', error);
      throw error;
    }
  }

  // Get popular services analytics
  static async getPopularServices(salonId?: string): Promise<Array<{ serviceName: string; bookings: number }>> {
    try {
      const constraints = salonId 
        ? [where('salonId', '==', salonId)]
        : [];

      const bookings = await FirestoreService.getWithQuery<Booking>('bookings', constraints);
      
      // Get all services to map IDs to names
      const services = await FirestoreService.getAll<Service>('services');
      const serviceMap = new Map(services.map(service => [service.id, service.name]));

      // Count bookings by service
      const serviceCounts: Record<string, number> = {};
      bookings.forEach(booking => {
        const serviceName = serviceMap.get(booking.serviceId) || 'Unknown Service';
        serviceCounts[serviceName] = (serviceCounts[serviceName] || 0) + 1;
      });

      // Convert to array and sort by count
      return Object.entries(serviceCounts)
        .map(([serviceName, bookings]) => ({ serviceName, bookings }))
        .sort((a, b) => b.bookings - a.bookings)
        .slice(0, 5); // Top 5 services
    } catch (error) {
      console.error('Error getting popular services:', error);
      throw error;
    }
  }

  // Get peak hours analytics
  static async getPeakHours(salonId?: string): Promise<Array<{ hour: string; bookings: number }>> {
    try {
      const constraints = salonId 
        ? [where('salonId', '==', salonId)]
        : [];

      const bookings = await FirestoreService.getWithQuery<Booking>('bookings', constraints);

      // Count bookings by hour
      const hourCounts: Record<string, number> = {};
      bookings.forEach(booking => {
        const hour = booking.time;
        hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      });

      // Convert to array and sort by hour
      return Object.entries(hourCounts)
        .map(([hour, bookings]) => ({ hour, bookings }))
        .sort((a, b) => a.hour.localeCompare(b.hour));
    } catch (error) {
      console.error('Error getting peak hours:', error);
      throw error;
    }
  }

  // Get salon performance analytics (admin only)
  static async getSalonPerformance(): Promise<Array<{ salonName: string; revenue: number; bookings: number }>> {
    try {
      const salons = await FirestoreService.getAll<Salon>('salons');
      const allBookings = await FirestoreService.getAll<Booking>('bookings');

      const salonPerformance = salons.map(salon => {
        const salonBookings = allBookings.filter(booking => booking.salonId === salon.id);
        const revenue = salonBookings
          .filter(booking => booking.status === 'completed')
          .reduce((sum, booking) => sum + booking.totalAmount, 0);

        return {
          salonName: salon.name,
          revenue,
          bookings: salonBookings.length
        };
      });

      return salonPerformance.sort((a, b) => b.revenue - a.revenue);
    } catch (error) {
      console.error('Error getting salon performance:', error);
      throw error;
    }
  }
}
