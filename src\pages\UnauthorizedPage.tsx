import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Heart } from 'lucide-react';

const UnauthorizedPage = () => {
  return (
    <div className="min-h-screen bg-glamspot-neutral-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center gap-2 text-glamspot-primary mb-8">
          <div className="w-10 h-10 bg-glamspot-primary rounded-lg flex items-center justify-center">
            <Heart className="w-6 h-6 text-white fill-current" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight">GlamSpot</h1>
        </div>

        <Card className="shadow-lg border-0">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-glamspot-neutral-900">
              Access Denied
            </CardTitle>
            <CardDescription className="text-glamspot-neutral-600">
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          
          <CardContent className="text-center space-y-4">
            <p className="text-sm text-glamspot-neutral-600">
              This page requires special permissions that your account doesn't have.
              Please contact an administrator if you believe this is an error.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button asChild variant="outline">
                <Link to="/">
                  Go to Homepage
                </Link>
              </Button>
              <Button asChild>
                <Link to="/login">
                  Sign In
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
